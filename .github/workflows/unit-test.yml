name: Run API Unit Tests

on:
  pull_request:
    branches: [main]

jobs:
  unit:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Install dependencies
        run: |
          cd services/api
          npm ci

      - name: Run API unit tests
        run: |
          cd services/api
          npm run test:unit

      - name: Run API unit tests with coverage
        run: |
          cd services/api
          npm run test:unit:coverage
