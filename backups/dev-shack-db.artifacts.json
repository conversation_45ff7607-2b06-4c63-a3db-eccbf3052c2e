[{"_id": {"$oid": "6672a33ed6c79b885977dd5b"}, "_internal": {"banned": false, "reviewed": false}, "settings": {"favorite": true}, "title": "Passport course playlist", "description": "A full playlist of a course on youtube for learning auth in node api", "url": "https://www.youtube.com/watch?v=YCeH_3IQmWw&list=PLUUOvUPrFYcjel-IKiAQY4coJJ9EUjfcP&index=2", "type": "video", "owner": {"$oid": "6673cd7a89fc71b15dde6b07"}, "tags": ["node", "js"], "notes": [], "status": "not started", "linkedTopics": [], "createdBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "updatedBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "permissions": {"accessLevel": "public", "sharedWith": [], "roles": {"viewer": [], "editor": [], "admin": []}}, "metadata": {"title": "2 - Node.js API + Passport - Local Authentication Strategy - Installing Express", "description": "➡️ - Weekly tutorials here: https://blog.devshack.techThis is the second lesson of a short course aimed at learning how to setup Passport Local Authenticatio...", "image": {"url": "https://i.ytimg.com/vi/YCeH_3IQmWw/maxresdefault.jpg", "height": "720", "width": "1280"}, "video": {"url": "https://www.youtube.com/watch?v=YCeH_3IQmWw&list=PLUUOvUPrFYcjel-IKiAQY4coJJ9EUjfcP&index=2", "height": "720", "width": "1280", "tag": "how to add auth to node api"}, "site": "YouTube"}, "updated": 1718788926811, "created": 1718788926811, "__v": 0}, {"_id": {"$oid": "687fd61760730815114c4438"}, "_internal": {"banned": false, "reviewed": false}, "settings": {"favorite": true}, "title": "Bubble Tea Deliveries", "description": "Amount of bubble teas that were delivered at Humanity for 2024", "url": "", "type": "docs", "owner": {"$oid": "68764e5d3eaf33a52d5851bd"}, "tags": ["node", "js"], "notes": [], "status": "completed", "linkedTopics": [], "createdBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "updatedBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "permissions": {"accessLevel": "private", "sharedWith": [], "roles": {"viewer": [], "editor": [], "admin": []}}, "metadata": {"title": "", "description": "", "image": {"url": "https://upload.wikimedia.org/wikipedia/commons/a/a2/Bubble_Tea.png", "height": "720", "width": "1280"}, "video": {"url": "", "height": "720", "width": "1280", "tag": ""}, "site": "Google Scholar"}, "updated": 1721659200000, "created": 1721659200000, "__v": 0}, {"_id": {"$oid": "687fe2b6ca4b61593c52497a"}, "_internal": {"banned": false, "reviewed": false}, "settings": {"favorite": false}, "title": "Is the Duolingo Owl Truly Evil?", "description": "A deep dive into the ethics of the most famous language learning app", "url": "", "type": "ebook", "owner": {"$oid": "687fe2d51a180a07caa7d0ad"}, "tags": ["node", "js"], "notes": [], "status": "in progress", "linkedTopics": [], "createdBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "updatedBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "permissions": {"accessLevel": "squad", "sharedWith": [], "roles": {"viewer": [], "editor": [], "admin": []}}, "metadata": {"title": "", "description": "", "image": {"url": "https://memesthatpay.com/wp-content/uploads/2024/03/evil-duolingo-owl-1024x538.webp", "height": "720", "width": "1280"}, "video": {"url": "", "height": "720", "width": "1280", "tag": ""}, "site": "Kindle"}, "updated": 1738544692964, "created": 1738544692964, "__v": 0}, {"_id": {"$oid": "687fe6b5b85d1e5a5efc23a0"}, "_internal": {"banned": false, "reviewed": true}, "settings": {"favorite": true}, "title": "<PERSON> vs. <PERSON> Competition", "description": "An enthralling showcase of superhuman talent.", "url": "", "type": "article", "owner": {"$oid": "68764e5d3eaf33a52d5851bd"}, "tags": ["node", "js"], "notes": [], "status": "not started", "linkedTopics": [], "createdBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "updatedBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "permissions": {"accessLevel": "public", "sharedWith": [], "roles": {"viewer": [], "editor": [], "admin": []}}, "metadata": {"title": "", "description": "", "image": {"url": "https://absaloncph.dk/wp-content/uploads/2024/09/0-2.jpg", "height": "720", "width": "1280"}, "video": {"url": "", "height": "720", "width": "1280", "tag": ""}, "site": "New York Times"}, "updated": 1690555597301, "created": 1690555597301, "__v": 0}, {"_id": {"$oid": "687fea3035349ccb728291d5"}, "_internal": {"banned": true, "reviewed": false}, "settings": {"favorite": true}, "title": "Noodle Addiction: <PERSON><PERSON><PERSON>'s Story", "description": "How a developer started to accept her noodle addiction.", "url": "", "type": "blog", "owner": {"$oid": "687fe2d51a180a07caa7d0ad"}, "tags": ["node", "js"], "notes": [], "status": "completed", "linkedTopics": [], "createdBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "updatedBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "permissions": {"accessLevel": "private", "sharedWith": [], "roles": {"viewer": [], "editor": [], "admin": []}}, "metadata": {"title": "", "description": "", "image": {"url": "https://jebnalak.com/cdn/shop/products/8801073113893.jpg?v=1679573327", "height": "720", "width": "1280"}, "video": {"url": "", "height": "720", "width": "1280", "tag": ""}, "site": "Instagram"}, "updated": 1727280433688, "created": 1727280433688, "__v": 0}, {"_id": {"$oid": "687feb8138651437abb49673"}, "_internal": {"banned": false, "reviewed": false}, "settings": {"favorite": true}, "title": "<PERSON><PERSON> says JavaScript is difficult", "description": "Famous Thought Leader talks about the difficulties of JavaScript", "url": "", "type": "tweet", "owner": {"$oid": "68764e5d3eaf33a52d5851bd"}, "tags": ["node", "js"], "notes": [], "status": "completed", "linkedTopics": [], "createdBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "updatedBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "permissions": {"accessLevel": "private", "sharedWith": [], "roles": {"viewer": [], "editor": [], "admin": []}}, "metadata": {"title": "", "description": "", "image": {"url": "https://media.istockphoto.com/id/1156837650/photo/javascript-code-computer-language-programming-internet-text-editor-components-display-screen.jpg?s=612x612&w=0&k=20&c=pMRRYB9XwMF2M5Z5cfgprREyZf6xo5Li-jP0qDrFF-Y=", "height": "720", "width": "1280"}, "video": {"url": "", "height": "720", "width": "1280", "tag": ""}, "site": "Twitter"}, "updated": 1703330889156, "created": 1703330889156, "__v": 0}, {"_id": {"$oid": "687feeb5fdeec78a0cb33cd4"}, "_internal": {"banned": false, "reviewed": false}, "settings": {"favorite": true}, "title": "Easy Crypto", "description": "Blockchain courses from Prafina from R10 000, available in English and French", "url": "", "type": "website", "owner": {"$oid": "68764e5d3eaf33a52d5851bd"}, "tags": ["node", "js"], "notes": [], "status": "completed", "linkedTopics": [], "createdBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "updatedBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "permissions": {"accessLevel": "private", "sharedWith": [], "roles": {"viewer": [], "editor": [], "admin": []}}, "metadata": {"title": "", "description": "", "image": {"url": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT325wQo1bdkFeNcqggMCQhx1Zc6f7mHgCNXg&s", "height": "720", "width": "1280"}, "video": {"url": "", "height": "720", "width": "1280", "tag": ""}, "site": "prafina.ai"}, "updated": 1752351460839, "created": 1752351460839, "__v": 0}, {"_id": {"$oid": "687ff027088ee76529854be7"}, "_internal": {"banned": false, "reviewed": false}, "settings": {"favorite": true}, "title": "Who is <PERSON><PERSON><PERSON>?", "description": "Exciting new series investigating the elusive 6th member of DevShack!", "url": "", "type": "other", "owner": {"$oid": "68764e5d3eaf33a52d5851bd"}, "tags": ["node", "js"], "notes": [], "status": "completed", "linkedTopics": [], "createdBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "updatedBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "permissions": {"accessLevel": "private", "sharedWith": [], "roles": {"viewer": [], "editor": [], "admin": []}}, "metadata": {"title": "", "description": "", "image": {"url": "https://i.ytimg.com/vi/1VEu2v0p7Bo/maxresdefault.jpg", "height": "720", "width": "1280"}, "video": {"url": "", "height": "720", "width": "1280", "tag": ""}, "site": "Amazon Prime"}, "updated": 1740889987574, "created": 1740889987574, "__v": 0}, {"_id": {"$oid": "687ff11ad4803a2107d3d75f"}, "_internal": {"banned": false, "reviewed": false}, "settings": {"favorite": true}, "title": "Thing", "description": "Postmodern perspective of objectivism and epistemology.", "url": "", "type": "docs", "owner": {"$oid": "68764e5d3eaf33a52d5851bd"}, "tags": ["node", "js"], "notes": [], "status": "completed", "linkedTopics": [], "createdBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "updatedBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "permissions": {"accessLevel": "private", "sharedWith": [], "roles": {"viewer": [], "editor": [], "admin": []}}, "metadata": {"title": "", "description": "", "image": {"url": "https://plus.unsplash.com/premium_photo-1682308429561-930e3df7ca6a?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8cGhpbG9zb3BoeXxlbnwwfHwwfHx8MA%3D%3D", "height": "720", "width": "1280"}, "video": {"url": "", "height": "720", "width": "1280", "tag": ""}, "site": "Nonsense University"}, "updated": 1742473099694, "created": 1742473099694, "__v": 0}, {"_id": {"$oid": "687ff229e8caa4cd78d3cff6"}, "_internal": {"banned": false, "reviewed": false}, "settings": {"favorite": true}, "title": "Why Ancient Greece is better than Atlantis", "description": "Self-taught historian's honest opinions caught on camera!", "url": "", "type": "video", "owner": {"$oid": "68764e5d3eaf33a52d5851bd"}, "tags": ["node", "js"], "notes": [], "status": "completed", "linkedTopics": [], "createdBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "updatedBy": {"$oid": "68764e5d3eaf33a52d5851bd"}, "permissions": {"accessLevel": "private", "sharedWith": [], "roles": {"viewer": [], "editor": [], "admin": []}}, "metadata": {"title": "", "description": "", "image": {"url": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRf_j6Qtiqfh8xKFr9FGorgb9LL8qrrFdTTjw&s", "height": "720", "width": "1280"}, "video": {"url": "", "height": "720", "width": "1280", "tag": ""}, "site": "YouTube"}, "updated": 1743091579841, "created": 1743091579841, "__v": 0}]