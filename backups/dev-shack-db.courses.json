[{"_id": {"$oid": "666ffdbc7189e097f4343631"}, "title": "<PERSON><PERSON>, <PERSON><PERSON>, Mongo - The Path to advanced Development.", "description": "The Path to advanced Development for webdevs", "modules": [{"module": 1, "title": "Project Initialisation", "lessons": [{"lesson": 1.1, "title": "Module Objectives", "content": "", "videoLink": "https://player.vimeo.com/video/960230804?badge=0&amp;autopause=0&amp;player_id=0&amp;app_id=58479", "_id": {"$oid": "666ffdbc7189e097f4343633"}}, {"lesson": 1.2, "title": "Setting up the Project Folder", "content": "", "videoLink": "", "_id": {"$oid": "666ffdbc7189e097f4343634"}}, {"lesson": 1.3, "title": "Creating an Express HTTP Server", "content": "", "videoLink": "", "_id": {"$oid": "666ffdbc7189e097f4343635"}}, {"lesson": 1.4, "title": "Setting up Environment Variables", "content": "", "videoLink": "", "_id": {"$oid": "666ffdbc7189e097f4343636"}}, {"lesson": 1.5, "title": "Creating a Config File in our App", "content": "", "videoLink": "", "_id": {"$oid": "666ffdbc7189e097f4343637"}}], "_id": {"$oid": "666ffdbc7189e097f4343632"}}, {"module": 2, "title": "Setting up <PERSON><PERSON>", "lessons": [{"lesson": 2.2, "title": "A Quick Explanation of Docker", "content": "", "videoLink": "", "_id": {"$oid": "666ffdbc7189e097f4343639"}}, {"lesson": 2.3, "title": "Installing Docker on Your Local Machine", "content": "", "videoLink": "", "_id": {"$oid": "666ffdbc7189e097f434363a"}}, {"lesson": 2.4, "title": "Creating a Dockerfile for Our App", "content": "", "videoLink": "", "_id": {"$oid": "666ffdbc7189e097f434363b"}}, {"lesson": 2.5, "title": "Exploring Our Docker Images and Containers", "content": "", "videoLink": "", "_id": {"$oid": "666ffdbc7189e097f434363c"}}, {"lesson": 2.6, "title": "A Brief Look at Overwriting in Docker", "content": "", "videoLink": "", "_id": {"$oid": "666ffdbc7189e097f434363d"}}, {"lesson": 2.7, "title": "Creating Convenient npm Scripts for Docker Commands", "content": "", "videoLink": "", "_id": {"$oid": "666ffdbc7189e097f434363e"}}], "_id": {"$oid": "666ffdbc7189e097f4343638"}}], "publised": false, "creator": "", "settings": {"tier": "premium"}, "__v": 0}, {"_id": {"$oid": "66725647d6eb744fb1ffd3d3"}, "title": "Node Express API Authentication with Passport", "description": "Local Authentication Strategy", "coverImage": "https://img-c.udemycdn.com/course/480x270/5820756_8a2d_5.jpg", "modules": [{"module": 1, "title": "Node HTTP Server Creation", "lessons": [{"lesson": 1.1, "title": "Strategy - Project Initialization", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3d5"}}, {"lesson": 1.2, "title": "Using Express & HTTP Server Setup", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3d6"}}, {"lesson": 1.3, "title": "Implementing our First API Route", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3d7"}}, {"lesson": 1.4, "title": "Strategy - Nodemon and Hot Reloading", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3d8"}}], "_id": {"$oid": "66725647d6eb744fb1ffd3d4"}}, {"module": 2, "title": "Building the Express Router", "lessons": [{"lesson": 2.1, "title": "Abstracting the Express Router", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3da"}}, {"lesson": 2.2, "title": "Adding Router to the Express Server", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3db"}}, {"lesson": 2.3, "title": "Strategy - Setting Up a Register Route", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3dc"}}, {"lesson": 2.4, "title": "Creating the Login and Logout Routes", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3dd"}}, {"lesson": 2.5, "title": "Catching Routes that are Not Defined", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3de"}}], "_id": {"$oid": "66725647d6eb744fb1ffd3d9"}}, {"module": 3, "title": "Implementing a User Model", "lessons": [{"lesson": 3.1, "title": "Implementing a User Class", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3e0"}}, {"lesson": 3.2, "title": "Implementing User Class Validation", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3e1"}}, {"lesson": 3.3, "title": "Adding Validation on User Model", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3e2"}}, {"lesson": 3.4, "title": "Adding Validation on the User Class", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3e3"}}, {"lesson": 3.5, "title": "Hashing Passwords with Bcrypt", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3e4"}}, {"lesson": 3.6, "title": "Setting Up Login Post Route to Model", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3e5"}}], "_id": {"$oid": "66725647d6eb744fb1ffd3df"}}, {"module": 4, "title": "Setting Up a Mock DB", "lessons": [{"lesson": 4.1, "title": "Authentication Strategy - Mocking a DB", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3e7"}}, {"lesson": 4.2, "title": "Authentication Strategy - DB Class Methods", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3e8"}}], "_id": {"$oid": "66725647d6eb744fb1ffd3e6"}}, {"module": 5, "title": "Setting Up Passport Local Strategy", "lessons": [{"lesson": 5.1, "title": "Strategy - Overview of the Auth Flow", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3ea"}}, {"lesson": 5.2, "title": "Installing and Configuring Passport Local", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3eb"}}, {"lesson": 5.3, "title": "Authenticating the Login Request", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3ec"}}, {"lesson": 5.4, "title": "Setting Up Session Middleware", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3ed"}}, {"lesson": 5.5, "title": "Validating Email and Password", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3ee"}}, {"lesson": 5.6, "title": "Complete Auth Flow in Login Route and Testing", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3ef"}}, {"lesson": 5.7, "title": "Strategy - Deserializing the User", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3f0"}}], "_id": {"$oid": "66725647d6eb744fb1ffd3e9"}}, {"module": 6, "title": "Creating Authenticated Routes", "lessons": [{"lesson": 6.1, "title": "Configuring Authenticated Routes Logic", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3f2"}}, {"lesson": 6.2, "title": "Testing Authenticated User Endpoint", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3f3"}}, {"lesson": 6.3, "title": "Testing Object to Send Back 403", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3f4"}}, {"lesson": 6.4, "title": "Strategy - Getting User from DB", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3f5"}}, {"lesson": 6.5, "title": "Custom Middleware Auth", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3f6"}}], "_id": {"$oid": "66725647d6eb744fb1ffd3f1"}}, {"module": 7, "title": "Building the Frontend", "lessons": [{"lesson": 7.1, "title": "Strategy - Setting Up Live Server", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3f8"}}, {"lesson": 7.2, "title": "Strategy - Styling the Home Page", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3f9"}}, {"lesson": 7.3, "title": "Strategy - Implementing the Login Form", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3fa"}}, {"lesson": 7.4, "title": "Strategy - Creating the Register Form", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3fb"}}, {"lesson": 7.5, "title": "Strategy - Wiring Up the Dashboard", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3fc"}}], "_id": {"$oid": "66725647d6eb744fb1ffd3f7"}}, {"module": 8, "title": "Integrating Login and User Info", "lessons": [{"lesson": 8.1, "title": "Adding Login State to User Info", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3fe"}}, {"lesson": 8.2, "title": "Connecting Register Form to JavaScript Map", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd3ff"}}, {"lesson": 8.3, "title": "Connecting Login Request to the Register Endpoint", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd400"}}, {"lesson": 8.4, "title": "Creating API and Handling Success Case", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd401"}}, {"lesson": 8.5, "title": "Adding Login Form and UI Improvements", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd402"}}], "_id": {"$oid": "66725647d6eb744fb1ffd3fd"}}, {"module": 9, "title": "Course Conclusion", "lessons": [{"lesson": 9.1, "title": "Adding <PERSON> Handler for <PERSON><PERSON>", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd404"}}, {"lesson": 9.2, "title": "Adding Audio Enhancement", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd405"}}, {"lesson": 9.3, "title": "Fixing the Login Request", "content": "", "videoLink": "", "_id": {"$oid": "66725647d6eb744fb1ffd406"}}], "_id": {"$oid": "66725647d6eb744fb1ffd403"}}], "publised": false, "creator": "666065f79185260b6bd09f94", "settings": {"tier": "free"}, "__v": 0}]