[{"_id": {"$oid": "666181459930bc16acb42e34"}, "title": "Getting Started with Node.js", "content": "# Getting Started with Node.js\n\nNode.js is a powerful JavaScript runtime. Here's how you can get started:\n\n1. Install Node.js from [Node.js website](https://nodejs.org/).\n2. Create your first script:\n```javascript\nconsole.log('Hello, Node.js!');\n```\n", "createdBy": {"$oid": "666065f79185260b6bd09f94"}, "created": {"$date": "2024-06-06T09:28:37.098Z"}, "updated": {"$date": "2024-06-06T09:28:37.102Z"}, "__v": 0}, {"_id": {"$oid": "666181b09930bc16acb42e36"}, "title": "Understanding Asynchronous Programming", "content": "## Understanding Asynchronous Programming\n\nIn Node.js, asynchronous programming is a key concept. Here's an example of an asynchronous function:\n\n```javascript\nconst fs = require('fs');\n\nfs.readFile('file.txt', 'utf8', (err, data) => {\n  if (err) {\n    console.error(err);\n    return;\n  }\n  console.log(data);\n});\n```\n", "createdBy": {"$oid": "60d5f60a93e5c22384f8a7b2"}, "created": {"$date": "2024-06-06T09:30:24.434Z"}, "updated": {"$date": "2024-06-06T09:30:24.436Z"}, "__v": 0}, {"_id": {"$oid": "666181b89930bc16acb42e38"}, "title": "Using npm for Package Management", "content": "### Using npm for Package Management\n\nnpm (Node Package Manager) is used to manage packages in Node.js. Here are some basic commands:\n\n- To initialize a new project:\n```sh\nnpm init\n```\n\n- To install a package:\n```sh\nnpm install <package-name>\n```\n", "createdBy": {"$oid": "60d5f60a93e5c22384f8a7b2"}, "created": {"$date": "2024-06-06T09:30:32.732Z"}, "updated": {"$date": "2024-06-06T09:30:32.734Z"}, "__v": 0}]