version: '3.8'
services:
  mongo:
    container_name: dev-shack-db
    image: mongo:latest
    restart: unless-stopped
    env_file: .env.development
    ports:
      - '27017:27017'
    volumes:
      - ./data:/data/db
    networks:
      - dev-shack-network

  api:
    container_name: dev-shack-api
    build: ./services/api
    restart: always
    ports:
      - '3000:3000'
      - '9229:9229'
    volumes:
      - ./services/api:/user/src/app
    networks:
      - dev-shack-network
    depends_on:
      - mongo

networks:
  dev-shack-network:
    driver: bridge
