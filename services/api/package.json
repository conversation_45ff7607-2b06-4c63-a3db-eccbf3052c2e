{"name": "dev-shack-api", "version": "1.0.0", "description": "", "type": "module", "main": "src/index.js", "scripts": {"start:dev": "nodemon --inspect=0.0.0.0:9229 --env-file=.env.development src/index.js", "docker:build": "docker build -t dev-shack/express-api:latest-dev .", "docker:run": "docker run -p 3000:3000 -v ./:/user/src/app --name api dev-shack/express-api:latest-dev", "docker:stop": "docker stop api", "test": "jest --verbose", "test:unit": "node --experimental-vm-modules node_modules/jest/bin/jest.js src/tests/unit", "test:unit:coverage": "node --experimental-vm-modules node_modules/jest/bin/jest.js --coverage src/tests/unit"}, "keywords": [], "author": "<PERSON>", "license": "ISC", "dependencies": {"@casl/ability": "^6.7.1", "@casl/mongoose": "^8.0.1", "axios": "^1.7.2", "compression": "^1.7.4", "cookie-encrypter": "^1.0.1", "cookie-parser": "^1.4.6", "cookie-session": "^2.1.0", "cors": "^2.8.5", "express": "^4.19.2", "express-rate-limit": "^7.3.0", "helmet": "^7.1.0", "html-entities": "^2.5.2", "http-proxy-middleware": "^3.0.0", "mongoose": "^8.0.4", "nodemon": "^3.0.1", "open-graph-scraper": "^6.5.2", "passport": "^0.7.0", "passport-google-oauth2": "^0.2.0", "pino": "^9.1.0", "socket.io": "^4.7.5", "validate.js": "^0.13.1"}, "devDependencies": {"jest": "^29.7.0", "pino-pretty": "^11.1.0", "superagent": "^9.0.2", "supertest": "^7.1.3"}}