import { ForbiddenError } from '@casl/ability';

export class PermissionError extends ForbiddenError {
  constructor(role, action, subject) {
    super();
    this.message = `Your current membership plan is '${role}'. You do not have permission to access this feature.  In order to ${action} ${subject}'s, you will need to upgrade your membership plan. Would you like to upgrade now?`;
    this.name = 'PermissionsError';
    this.type = 'permissions';
    this.timestamp = Date.now();
    this.code = 403;
  }
}
