import { logger } from '../lib/util/logger.js';

export default class EventBus {
  constructor() {
    EventBus.__singleton = this;
    this.listeners = [];
  }

  register(listener) {
    logger.debug(`Registering listener`);
    this.listeners.push(listener);
  }

  notify(eventType, payload) {
    logger.debug(
      `Received ${eventType} event - notifying all interested listeners`
    );
    this.listeners.forEach((notifyListener) =>
      notifyListener(eventType, payload)
    );
  }

  static instance() {
    return EventBus.__singleton === undefined
      ? new EventBus()
      : EventBus.__singleton;
  }
}

export const eventBus = () => EventBus.instance();
