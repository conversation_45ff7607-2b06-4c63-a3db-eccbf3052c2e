import { NEW_ARTIFACT_CREATED } from '../eventTypes.js';
import { logger } from '../../lib/util/logger.js';
import { PointsEvents } from '../../models/types/index.js';
import { PointsService } from '../../services/user/PointsService.js';
import { getSocketIO } from '../../lib/sockets/socket.js';

export const onPointsEarned = (eventType, payload) => {
  if (eventType === NEW_ARTIFACT_CREATED) {
    // do something here
    logger.info(`New artifact created!`);

    const { points, ref } = PointsEvents.NEW_PUBLIC_ARTIFACT;

    try {
      PointsService.updatePoints(payload, ref, points).then((activity) => {
        // If activity is falsy this means the daily cap has been reached.
        // If activity is truthy, then the user has earned points.
        if (activity) {
          const io = getSocketIO();
          io.emit('server-message', 'Hello from the server!');
          io.emit('points-earned', activity);
        }
      });
    } catch (err) {
      logger.error(`Failed to update points: ${err}`);
    }
  }

  if (eventType === `NEW_ARTIFACT_COMMENT`) {
    // do something here
  }
};
