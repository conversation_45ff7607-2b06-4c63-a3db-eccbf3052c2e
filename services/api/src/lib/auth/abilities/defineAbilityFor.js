import { createMongoAbility, AbilityBuilder } from '@casl/ability';

export const defineAbilityFor = (req, res = {}, options = {}) => {
  const { user } = req;
  if (!user) return false;
  return defineAbilityForUser(user, req, res, options);
};

export const defineAbilityForUser = (user, req, res = {}, options = {}) => {
  const { can, cannot, build } = new AbilityBuilder(createMongoAbility);

  if (user.role === 'admin') {
    can('manage', 'all');
  } else if (user.role === 'founder') {
    can('read', ['Course', 'Artifact', 'Note', 'Community', 'Feedback']);
    can('write', ['Course', 'Artifact', 'Note', 'Community', 'Feedback']);
    can('update', ['Artifact', 'Note', 'Community']);
  } else if (user.role === 'premium') {
    can('read', ['Course', 'Artifact', 'Note', 'Community', 'Feedback']);
    can('write', ['Course', 'Artifact', 'Note', 'Feedback']);
    can('update', ['Course', 'Artifact', 'Note']);
    can('delete', ['Course', 'Artifact', 'Note']);
  } else if (user.role === 'basic') {
    can('read', 'Course', { free: true });
    can('read', 'Note', { createdBy: user._id });
    can('write', 'Note');
  } else if (user.role === 'guest') {
    //can('read', 'Artifact');
    can('write', 'write');
  } else {
    can('read', 'PublicContent');
  }

  return build();
};
