import passport from 'passport';
import { User } from '../../models/UserSchema.js';

export const setupPassport = (app) => {
  // Initialising passport (with session support);
  app.use(passport.initialize({}));
  app.use(passport.session({}));

  // Used to stuff info into cookie
  passport.serializeUser((user, done) => {
    // We can add roles, badges, points etc to the cookie
    try {
      done(null, user.id);
    } catch (err) {
      done(new Error('Failed to serialize user'));
    }
  });

  // Decodes cookie and persists session
  passport.deserializeUser((id, done) => {
    User.findById(id)
      .then((user) => {
        done(null, user);
      })
      .catch((e) => {
        done(new Error(`Failed to deserialize user: ${e.toString()}`));
      });
  });
};
