export default {
  port: process.env.PORT || 3000,

  /**
   * Rest API config
   */
  api: {
    prefix: process.env.API_PREFIX || '/api',
  },

  /**
   * Switch routes on/off
   */
  routes: {
    default: true,
    user: true,
    auth: true,
    artifact: true,
    note: true,
    course: true,
    feedback: true,
    proxy: true,
    links: true,
  },

  streaks: {
    enabled: true,
  },

  mongo: {
    protocol: process.env.MONGO_PROTOCOL,
    username: process.env.MONGO_USERNAME,
    password: process.env.MONGO_PASSWORD,
    database: process.env.MONGO_DB_NAME,
    url: process.env.MONGO_URL,
  },

  /**
   * Logging configuration.
   */
  log: {
    level: process.env.LOG_LEVEL || 'info',
  },

  /**
   * Cookie secret for encrypting.
   */
  cookie: {
    secret: process.env.COOKIE_SECRET || 'VLkPC7VjpuAk/GzvYqDgT5VJ8tj/7A0a',
    maxAgeMinutes: process.env.COOKIE_MAX_AGE_MINUTES || 480, // cookie expires after this many minutes of inactivity
  },

  /**
   * CORS settings.
   */
  cors: {
    allowedOrigins: process.env.ALLOWED_ORIGINS
      ? process.env.ALLOWED_ORIGINS.split(',')
      : [],
  },

  /**
   * Backend routes.
   */
  backend: {
    host: process.env.API_HOST,
  },
  /**
   * Frontend routes.
   */
  frontend: {
    host: process.env.FRONTEND_HOST,
    authPrefix: process.env.FRONTEND_AUTH_PREFIX,
    signupPage: process.env.FRONTEND_SIGN_UP_PAGE,
    shared: {
      path: 'shared',
    },
  },

  /**
   * Helmet configuration.
   */
  helmet: {
    rateLimit: {
      windowMinutes: process.env.HELMET_RATE_LIMIT_WINDOW_MINS || 5,
      limitMax: process.env.HELMET_RATE_LIMIT_MAX || 100,
    },
  },

  /**
   * Authentication settings.
   */
  auth: {
    google: {
      route: process.env.GOOGLE_AUTH,
      callbackPage: process.env.GOOGLE_AUTH_CALLBACK_PAGE,
      failedPage: process.env.GOOGLE_AUTH_FAILED_PAGE,
      clientId: process.env.GOOGLE_AUTH_CLIENT_ID,
      clientSecret: process.env.GOOGLE_AUTH_SECRET,
      callbackUrl: `${process.env.FRONTEND_API_HOST}${process.env.API_PREFIX}${process.env.FRONTEND_AUTH_PREFIX}${process.env.GOOGLE_AUTH_CALLBACK_PAGE}`,
    },
    github: {
      route: process.env.GITHUB_AUTH,
      failedPage: process.env.GITHUB_AUTH_FAILED_PAGE,
      callbackPage: process.env.GITHUB_AUTH_CALLBACK_PAGE,
      clientId: process.env.GITHUB_AUTH_CLIENT_ID,
      clientSecret: process.env.GITHUB_AUTH_SECRET,
      redirectUrl: `${process.env.FRONTEND_API_HOST}${process.env.API_PREFIX}${process.env.FRONTEND_AUTH_PREFIX}${process.env.GITHUB_AUTH_CALLBACK_PAGE}`,
    },
    local: {
      enabled: process.env.LOCAL_AUTH === 'enabled',
      failedPage: process.env.GOOGLE_AUTH_FAILED_PAGE,
    },
  },
};

