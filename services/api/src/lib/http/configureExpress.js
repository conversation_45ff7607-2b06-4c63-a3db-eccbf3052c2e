import cors from 'cors';
import helmet from 'helmet';
import express from 'express';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import cookieEncrypter from 'cookie-encrypter';

import { logger } from '../util/logger.js';
import { setupMongo } from '../mongodb/index.js';
import { corsOptions } from './corsOptions.js';
import { setupPassport } from '../auth/index.js';
import { cookieSecretKey } from './cookieSecretKey.js';
import { configureRateLimit } from './rateLimiter.js';
import { cookieSessionMiddleware } from './cookieSessionMiddleware.js';

/**
 * Used to configure the Express app instance.
 * Express app is passed in & middleware is configured
 *
 * @param app Express app instance
 * @param config Application config object
 * @returns Express app instance
 */
export const configureExpress = (app, config) => {
  const { cookie } = config;

  // Configure cookie lifetime
  app.use(cookieSessionMiddleware(cookie));
  logger.info(`Using session max age of ${cookie.maxAgeMinutes} minutes`);

  // register regenerate & save after the cookieSession middleware initialization
  app.use(function (req, res, next) {
    if (req.session && !req.session.regenerate) {
      req.session.regenerate = (cb) => {
        cb();
      };
    }
    if (req.session && !req.session.save) {
      req.session.save = (cb) => {
        cb();
      };
    }
    next();
  });

  const secretKey = cookieSecretKey(cookie.secret);

  app.use(express.urlencoded({ extended: true }));
  app.use(express.json());
  app.use(cookieParser(secretKey));
  app.use(cookieEncrypter(secretKey));

  app.use((req, res, next) => {
    if (req.session) {
      req.session.nowInMinutes = Math.floor(Date.now() / 60e3);
    }
    next();
  });

  app.use(cors(corsOptions(config)));

  logger.info(`Using express with GZIP compression`);
  app.use(compression());

  // load database
  setupMongo(config);
  logger.info('Mongo DB loaded and connected  ✌️');
  setupPassport(app);
  logger.info('Passport authentication is ready  ✌️');

  app.use(helmet());
  app.disable('x-powered-by');

  if (process.env.NODE_ENV === 'production') {
    app.use(configureRateLimit(config));
  }

  return app;
};
