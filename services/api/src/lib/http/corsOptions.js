export const corsOptions = ({ frontend, backend, cors }) => {
  const origins = [frontend.host, backend.host, ...cors.allowedOrigins];
  return {
    credentials: true,
    exposedHeaders: `WWW-Authenticate`,
    origin: (origin, callback) => {
      if (!origin || origin === 'null' || origins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error(`Origin: ${origin} is not allowed`));
      }
    },
  };
};
