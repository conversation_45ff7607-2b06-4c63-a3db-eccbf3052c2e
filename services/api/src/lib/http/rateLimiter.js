import { logger } from '../util/logger.js';
import rateLimit from 'express-rate-limit';

export const configureRateLimit = (config) => {
  const windowMins = config.helmet.rateLimit.windowMinutes;
  const requestLimit = config.helmet.rateLimit.limitMax;

  const limiter = rateLimit({
    windowMs: windowMins * 60 * 1000,
    max: requestLimit,
    message: {
      error: {
        timestamp: Date.now(),
        type: 'api-rate-limit',
        code: 429,
        messages: ['Too many requests, try again later.'],
        service: 'express',
        key: 'rate-limit',
      },
    },
    skip: (req) =>
      req.user !== undefined &&
      req.user.allowed !== undefined &&
      req.user.allowed,
  });

  logger.info(
    `Using Helmet with a rate limit of ${requestLimit} requests every ${windowMins} minutes`
  );

  return limiter;
};
