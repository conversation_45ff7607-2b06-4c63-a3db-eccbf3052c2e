import passport from 'passport';
import { Strategy as GitHubStrategy } from 'passport-github2';
import config from '../../config.js';
import { logger } from '../../util/logger.js';
import { AuthService } from '../../../services/auth/AuthService.js';

const getEmail = (profile) =>
  (profile.emails && profile.emails.length > 0 && profile.emails[0].value) ||
  profile._json.email || null;

const parseName = (profile) => {
  const fallback = profile.username || 'GitHub User';
  const fullName = profile.displayName?.trim() || fallback;
  const parts = fullName.split(/\s+/);
  const givenName = parts[0];
  const familyName = parts.length > 1 ? parts.slice(1).join(' ') : 'User';
  return { givenName, familyName };
};

const profileGivenName = (profile) => parseName(profile).givenName;
const profileFamilyName = (profile) => parseName(profile).familyName;


const getDomain = (profile) => profile._json.hd;
const getPicture = (profile) => profile._json.picture || '';
const getLocale = (profile) => profile._json.locale || '';
const getLanguage = (profile) => profile.language || null;
const getGender = (profile) => profile._json.gender || null;
export const githubAuthLoader = () => {
    passport.use(
        new GitHubStrategy(
            {
            clientID: config.auth.github.clientId,
            clientSecret: config.auth.github.clientSecret,
            callbackURL: config.auth.github.redirectUrl,
            scope: ['user:email'],
            passReqToCallback: true, 
            },
            async (req, accessToken, refreshToken, profile, done) => {
            AuthService.validate({
                httpRequest: req,
                provider: 'github',
                profile,
                email: getEmail(profile),
                accessToken,
                refreshToken,
                givenName: profileGivenName(profile),
                familyName: profileFamilyName(profile),
                domain: getDomain(profile) || 'github.com',
                isDomainValid: true,
                picture: getPicture(profile),
                emailVerified: true, 
                locale: getLocale(profile),
                language: getLanguage(profile),
                gender: getGender(profile),
                done,
               });
    
            }
        )
    );

}

