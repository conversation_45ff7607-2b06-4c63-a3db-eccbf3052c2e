import passport from 'passport';
import Strategy from 'passport-google-oauth2';
import config from '../../config.js';
import { logger } from '../../util/logger.js';
import { AuthService } from '../../../services/auth/AuthService.js';

// Auth service

const isDomainValid = (domain) => domain !== undefined;
const isEmailVerified = (profile) => profile._json.verified_email;
const profileGivenName = (profile) => profile._json.given_name || '';
const profileFamilyName = (profile) => profile._json.family_name || '';
const getDomain = (profile) => profile._json.hd;
const getPicture = (profile) => profile._json.picture || '';
const getLocale = (profile) => profile._json.locale || '';
const getLanguage = (profile) => profile.language || null;
const getGender = (profile) => profile._json.gender || null;

// Load Passport  Google auth config
export const googleAuthLoader = () => {
  passport.use(
    'google',
    new Strategy(
      {
        clientID: config.auth.google.clientId,
        clientSecret: config.auth.google.clientSecret,
        callbackURL: config.auth.google.callbackUrl, // TODO
        passReqToCallback: true,
      },
      async (req, accessToken, refreshToken, profile, done) =>
        AuthService.validate({
          httpRequest: req,
          provider: 'google',
          profile,
          email: profile._json.email,
          accessToken,
          refreshToken,
          givenName: profileGivenName(profile),
          familyName: profileFamilyName(profile),
          domain: getDomain(profile) || 'google.com',
          isDomainValid: isDomainValid(getDomain(profile)),
          picture: getPicture(profile),
          emailVerified: isEmailVerified(profile),
          locale: getLocale(profile),
          language: getLanguage(profile),
          gender: getGender(profile),
          done,
        })
    )
  );
};
