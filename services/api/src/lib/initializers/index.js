import { setupRoutes } from './setupRoutes.js';
import { logger } from '../util/logger.js';
import { googleAuthLoader } from './auth/googleAuthLoader.js';
import { eventBusLoader } from './eventbusLoader.js';
import { githubAuthLoader } from './auth/gitHubAuthLoader.js';

export const initializeApp = async (app, config) => {
  // Setup EventBus

  // Setup googleAuth
  googleAuthLoader();
  logger.info(`Google Auth is ready... ✅ `);

  //Setup gitAuth
  githubAuthLoader();
  logger.info(`Github Auth is ready... ✅ `);

  eventBusLoader();
  logger.info(`Event Bus is ready... ✅`);

  // Setup gihub Auth

  // Setup local auth

  // setup express router
  setupRoutes(app);
  logger.info(`Express router and app routes setup... ✅`);

  // setup websockets
};
