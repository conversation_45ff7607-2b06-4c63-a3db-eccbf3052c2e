import config from '../config.js';
import routes from '../../routes/index.js';
import { logger } from '../util/logger.js';

export const setupRoutes = (app) => {
  const router = routes();
  app.use(config.api.prefix, router);

  // Debug routes
  if (logger.level === 'trace') {
    printRoutes(app, router);
  }
};

const printRoutes = (app, router) => {
  const routes = [];
  let route;

  const processRoutes = (middleware) => {
    if (middleware.route) {
      // routes registered directly on the app
      routes.push(middleware.route);
    } else if (middleware.name === 'router') {
      // router middleware
      middleware.handle.stack.forEach((handler) => {
        route = handler.route;
        if (route) {
          routes.push(route);
        }
      });
    }
  };

  app._router.stack.forEach((middleware) => processRoutes(middleware));
  router.stack.forEach((middleware) => processRoutes(middleware));

  logger.trace(`## Registered routes:`);
  routes.forEach(({ path }) => {
    logger.trace(`  - ${path}`);
  });
};
