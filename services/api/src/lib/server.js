import express from 'express';
import config from './config.js';
import { logger } from '../lib/util/logger.js';
import { initializeApp } from './initializers/index.js';
import { initializeSocket } from './../lib/sockets/socket.js';
import { configureExpress } from '../lib/http/index.js';

import { createServer } from 'http';

export const startServer = async () => {
  const app = express();
  const port = config.port;

  // Configure express...
  configureExpress(app, config);

  // Initialize app...
  await initializeApp(app, config);

  const httpServer = createServer(app);
  const io = initializeSocket(httpServer);

  try {
    httpServer.listen(port, () => {
      logger.info(`Dev Shack api running on port ${port}...🚀`);
    });
  } catch (err) {
    throw new Error(err);
  }
};
