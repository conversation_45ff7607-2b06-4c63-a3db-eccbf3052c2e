import { Server } from 'socket.io';
import { logger } from '../util/logger.js';

let io;

export const initializeSocket = (server) => {
  io = new Server(server, {
    cors: {
      origin: 'http://localhost:4200',
      methods: ['GET', 'POST'],
    },
  });

  io.on('connection', (socket) => {
    logger.info('New client connected');

    socket.on('message', (message) => {
      logger.info(`Received message => ${message}`);
      socket.emit('message', `Server received: ${message}`);
    });

    socket.on('disconnect', () => {
      logger.info('Client disconnected');
    });

    socket.onAny((eventName, ...args) => {
      logger.debug(`Received event: "${eventName}", with arguments:`);
      logger.debug(args);
    });
  });

  return io;
};

export const getSocketIO = () => {
  if (!io) {
    throw new Error('Socket.io not initialized!');
  }
  return io;
};
