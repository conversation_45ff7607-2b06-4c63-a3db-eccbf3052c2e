import pino from 'pino';
import config from '../config.js';

const transportConfig = {
  transport: {
    target: 'pino-pretty',
    options: {
      colorize: true,
      singleLine: false,
      levelFirst: true,
      hideObject: false,
      ignore: 'pid,hostname',
    },
  },
};

const transport = process.env.NODE_ENV !== 'development' ? transportConfig : {};

export const logger = pino({
  level: config.log.level,
  ...transport,
  formatters: {
    level: (label) => {
      return { level: label?.toUpperCase() };
    },
  },
  redact: [
    'user.name',
    'user.email',
    'user.password',
    'user.token',
    'newUser.email',
    'currentUser.email',
    'email',
  ],
  timestamp: pino.stdTimeFunctions.isoTime,
  minLength: 1024,
  sync: false,
});
