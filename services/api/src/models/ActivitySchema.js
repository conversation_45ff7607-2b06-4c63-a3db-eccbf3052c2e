import mongoose from 'mongoose';
import { PointsEvents } from './types/index.js';

const {
  NEW_PUBLIC_ARTIFACT,
  SHARE_PUBLIC_ARTIFACT,
  ARTIFACT_COMMENT_RECIEVED,
  ARTIFACT_LIKE_RECIEVED,
} = PointsEvents;

const { Schema } = mongoose;

const ActivitySchema = new Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  type: {
    type: String,
    enum: [
      NEW_PUBLIC_ARTIFACT.ref,
      SHARE_PUBLIC_ARTIFACT.ref,
      ARTIFACT_COMMENT_RECIEVED.ref,
      ARTIFACT_LIKE_RECIEVED.ref,
      'studySession',
      'addReview',
      'sharePathway',
      'courseProgress',
      'socialShare',
      'appReview',
      'pluginDownload',
      'appDownload',
      'newsletterSubscribe',
    ],
    required: true,
  },
  points: {
    type: Number,
    required: true,
  },
  created: {
    type: Number,
    default: Date.now,
  },
});

ActivitySchema.pre('save', function (next) {
  const now = Date.now();
  const doc = this;
  if (!doc.created) {
    doc.created = now;
  }
  if (next) next();
});

ActivitySchema.pre('findOneAndUpdate', function (next) {
  this.set({ updated: Date.now() });
  if (next) next();
});

export const Activity = mongoose.model('Activity', ActivitySchema);
