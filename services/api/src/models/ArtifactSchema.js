import mongoose from 'mongoose';
import { accessibleBy } from '@casl/mongoose';

const { Schema } = mongoose;

const ArtifactSchema = new Schema({
  _internal: {
    banned: {
      type: Boolean,
      default: false,
    },
    reviewed: {
      type: Boolean,
      default: false,
    },
  },
  settings: {
    type: Object,
  },
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
  },
  url: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    enum: [
      'video',
      'article',
      'tweet',
      'ebook',
      'docs',
      'package',
      'blog',
      'website',
      'other',
    ],
    required: true,
  },
  owner: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  identifiers: {
    short: String,
    perUser: String,
  },
  tags: {
    type: [String],
  },
  notes: [
    {
      type: Schema.Types.ObjectId,
      ref: 'Note',
    },
  ],
  status: {
    type: String,
    enum: ['not started', 'in progress', 'completed'],
    default: 'not started',
  },
  created: Number,
  updated: Number,
  linkedTopics: [
    {
      type: Schema.Types.ObjectId,
      ref: 'Topic',
    },
  ],
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  permissions: {
    accessLevel: {
      type: String,
      enum: ['public', 'squad', 'private'],
      default: 'private',
    },
    sharedWith: [
      {
        type: Schema.Types.ObjectId,
        ref: 'User',
      },
    ],
    roles: {
      viewer: [
        {
          type: Schema.Types.ObjectId,
          ref: 'User',
        },
      ],
      editor: [
        {
          type: Schema.Types.ObjectId,
          ref: 'User',
        },
      ],
      admin: [
        {
          type: Schema.Types.ObjectId,
          ref: 'User',
        },
      ],
    },
  },
  metadata: {
    title: String,
    description: String,
    image: {
      url: String,
      height: String,
      width: String,
    },
    video: {
      url: String,
      secureUrl: String,
      height: String,
      width: String,
      tag: String,
    },
    twitterHandle: String,
    site: String,
  },
  likes: {
    type: Number,
    default: 0,
  },
  shares: {
    type: Number,
    default: 0,
  },
});

ArtifactSchema.plugin(accessibleBy);
ArtifactSchema.pre('save', function (next) {
  const now = Date.now();
  const doc = this;
  doc.updated = now;
  if (!doc.created) {
    doc.created = now;
  }
  if (next) next();
});

ArtifactSchema.pre('findOneAndUpdate', function (next) {
  this.set({ updated: Date.now() });
  if (next) next();
});

export const Artifact = mongoose.model('Artifact', ArtifactSchema);
