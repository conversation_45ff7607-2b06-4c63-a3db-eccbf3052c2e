import mongoose from 'mongoose';

const { Schema } = mongoose;

// Define the Lesson schema
const lessonSchema = new Schema({
  lesson: Number,
  title: {
    type: String,
    required: true,
  },
  content: {
    type: String,
  },
  videoLink: {
    type: String,
  },
});

// Define the Module schema
const moduleSchema = new Schema({
  module: Number,
  title: {
    type: String,
  },
  description: {
    type: String,
  },
  lessons: [lessonSchema],
});

// Define the Course schema
const courseSchema = new Schema({
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  coverImage: String,
  modules: [moduleSchema],
  created: Number,
  updated: Number,
  published: {
    type: Boolean,
    default: false,
  },
  creator: String,
  rating: Number,
  enrolments: {
    type: Number,
    default: 0,
  },
  settings: {
    tier: {
      type: String,
      enum: ['free', 'premium', 'basic'],
      default: 'premium',
      required: true,
    },
  },
});

courseSchema.post('findOne', function (doc) {
  if (doc) {
    const modulesCount = doc.modules.length;
    const lessonsCount = doc.modules.reduce(
      (total, module) => total + module.lessons.length,
      0
    );
    doc.set('modulesCount', modulesCount, { strict: false });
    doc.set('lessonsCount', lessonsCount, { strict: false });
  }
});
courseSchema.set('toJSON', { virtuals: true });
courseSchema.set('toObject', { virtuals: true });

// Create models for each schema
// const Lesson = mongoose.model('Lesson', lessonSchema);
// const Module = mongoose.model('Module', moduleSchema);
export const Course = mongoose.model('Course', courseSchema);
