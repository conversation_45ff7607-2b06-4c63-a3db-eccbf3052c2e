import mongoose from 'mongoose';

const { Schema } = mongoose;

const FeedbackSchema = new Schema(
  {
    owner: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    artifactId: {
      type: Schema.Types.ObjectId,
      ref: 'Artifact',
      required: true,
    },
    comment: {
      type: String,
      required: true,
      maxLength: 280,
    },
    rating: Number,
    created: {
      type: Number,
    },
    updated: {
      type: Number,
    },
    _internal: {},
    settings: {
      public: Boolean,
    },
  },
  { collection: 'feedback' }
);

FeedbackSchema.pre('save', function (next) {
  const now = Date.now();
  const doc = this;
  doc.updated = now;
  if (!doc.created) {
    doc.created = now;
  }
  if (next) next();
});

FeedbackSchema.pre('findOneAndUpdate', function (next) {
  this.set({ updated: Date.now() });
  if (next) next();
});

export const Feedback = mongoose.model('Feedback', FeedbackSchema);
