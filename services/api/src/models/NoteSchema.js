import mongoose from 'mongoose';

const { Schema } = mongoose;

const NoteSchema = new Schema({
  title: {
    type: String,
    required: true,
  },
  content: {
    type: String,
    required: true,
  },
  created: {
    type: Date,
    default: Date.now,
  },
  updated: {
    type: Date,
    default: Date.now,
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
});

NoteSchema.pre('save', function (next) {
  const now = Date.now();
  const doc = this;
  doc.updated = now;
  if (!doc.created) {
    doc.created = now;
  }
  if (next) next();
});

NoteSchema.pre('findOneAndUpdate', function (next) {
  this.set({ updated: Date.now() });
  if (next) next();
});

export const Note = mongoose.model('Note', NoteSchema);
