import mongoose from 'mongoose';

const { Schema } = mongoose;

const TopicSchema = new Schema({
  name: {
    type: String,
    required: true,
  },
  description: {
    type: String,
  },
  created: {
    type: Date,
    default: Date.now,
  },
  updated: {
    type: Date,
    default: Date.now,
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
});

TopicSchema.pre('save', function (next) {
  const now = Date.now();
  const doc = this;
  doc.updated = now;
  if (!doc.created) {
    doc.created = now;
  }
  if (next) next();
});

TopicSchema.pre('findOneAndUpdate', function (next) {
  this.set({ updated: Date.now() });
  if (next) next();
});

export const User = mongoose.model('Topic', TopicSchema);
