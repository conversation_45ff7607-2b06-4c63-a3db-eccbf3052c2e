import mongoose from 'mongoose';
import { Course } from './../models/CourseSchema.js';

const { Schema } = mongoose;

const UserCourseProgressSchema = new Schema(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    courseId: {
      type: Schema.Types.ObjectId,
      ref: 'Course',
      required: true,
    },
    completedLessons: [{ type: Schema.Types.ObjectId }],
    totalLessons: {
      default: 0,
      type: Number,
      required: true,
    },
    lastPosition: {
      type: Number,
      default: 0,
    },
    progress: {
      type: Number,
      default: 0,
    },
    currentLesson: Schema.Types.ObjectId,
  },
  { collection: 'user_course_progress' }
);

UserCourseProgressSchema.pre('save', function (next) {
  const now = Date.now();
  const doc = this;
  doc.updated = now;
  if (!doc.created) {
    doc.created = now;
  }
  if (next) next();
});

UserCourseProgressSchema.pre('findOneAndUpdate', async function (next) {
  this.options.runValidators = true;
  const update = this.getUpdate();
  const doc = await this.model.findOne(this.getQuery());
  if (doc) {
    const course = await Course.findById(doc.courseId);
    if (course) {
      const totalLessons = course.modules.reduce(
        (sum, module) => sum + module.lessons.length,
        0
      );
      const completedLessons = new Set([
        ...doc.completedLessons,
        // ...(update.$addToSet?.completedLessons || []),
      ]);
      update.$set.progress = (completedLessons.size / totalLessons) * 100;
    }
  }
  next();
});

UserCourseProgressSchema.pre('findOneAndUpdate', function (next) {
  this.set({ updated: Date.now() });
  if (next) next();
});

export const UserCourseProgress = mongoose.model(
  'UserCourseProgress',
  UserCourseProgressSchema
);
