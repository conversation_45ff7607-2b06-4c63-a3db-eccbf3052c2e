import mongoose from 'mongoose';
import validate from 'validate.js';

const constraints = {
  email: () => ({
    presence: { allowEmpty: false },
    email: true,
  }),
  name: () => {
    const regex = "[-'A-Za-z ]+";

    const constraints = {
      presence: { allowEmpty: false },
      type: 'string',
      format: {
        pattern: regex,
        flags: 'i',
      },
    };

    return constraints;
  },
};

const { Schema } = mongoose;

const UserSchema = new Schema({
  name: {
    first: {
      type: String,
      required: true,
      validate: {
        validator: (name) => !validate.single(name, constraints.name),
        message: (props) => `${props.value} is not a valid first name`,
      },
    },
    last: {
      type: String,
      required: true,
      validate: {
        validator: (name) => !validate.single(name, constraints.name),
        message: (props) => `${props.value} is not a valid last name`,
      },
    },
  },
  email: {
    type: String,
    required: true,
    unique: true,
    validate: {
      validator: (email) => !validate.single(email, constraints.email),
      message: (props) => `${props.value} is not a valid email address`,
    },
  },
  security: {
    profiles: Object,
    emailVerified: Boolean,
    tokens: Object,
  },
  gender: {
    type: String,
    enum: ['male', 'female', 'other', 'undefined'],
    defualt: 'undefined',
  },
  created: Date,
  updated: Date,
  activated: Boolean,
  banned: Boolean,
  language: String,
  locale: String,
  picture: String,
  marketing: {
    email: Boolean,
    sms: Boolean,
    push: Boolean,
  },
  policies: {
    terms: String,
    privacy: String,
  },
  membership: {
    plan: {
      name: String,
      id: String,
    },
    status: String,
    startDate: Date,
    endDate: Date,
  },
  role: {
    type: String,
    enum: ['guest', 'basic', 'premium', 'founder', 'admin'],
    default: 'guest',
  },
  points: {
    total: {
      type: Number,
      default: 0,
    },
  },
  level: {
    type: Number,
    default: 1,
  },
  streaks: {
    daily: {
      count: {
        type: Number,
        default: 0,
      },
      lastUpdated: {
        type: Date,
        default: Date.now,
      },
    },
    activity: {
      studySession: {
        count: {
          type: Number,
          default: 0,
        },
        lastUpdated: {
          type: Date,
          default: Date.now,
        },
      },
      courseProgress: {
        count: {
          type: Date,
          default: 0,
        },
        lastUpdated: {
          type: Date,
          default: Date.now,
        },
      },
    },
  },
  referrals: {
    code: String,
    referralCount: Number,
  },
  _internal: Object,
  _tags: Array,
  _notes: {
    admin: Array,
    cs: Array,
  },
});

UserSchema.pre('save', function (next) {
  const now = new Date();
  const doc = this;
  doc.updated = now;
  if (!doc.created) {
    doc.created = now;
  }
  if (next) next();
});

UserSchema.pre('findOneAndUpdate', function (next) {
  this.set({ updated: new Date() });
  if (next) next();
});

export const User = mongoose.model('User', UserSchema);
