{"title": "<PERSON><PERSON>, <PERSON><PERSON>, Mongo - The Path to advanced Development.", "Description": "The Path to advanced Development for webdevs", "modules": [{"module": 1, "title": "Project Initialisation", "duration": 28, "lessons": [{"lesson": 1.1, "title": "Module Objectives", "content": "", "videoLink": ""}, {"lesson": 1.2, "title": "Setting up the Project Folder", "content": "", "videoLink": ""}, {"lesson": 1.3, "title": "Creating an Express HTTP Server", "content": "", "videoLink": ""}, {"lesson": 1.4, "title": "Setting up Environment Variables", "content": "", "videoLink": ""}, {"lesson": 1.5, "title": "Creating a Config File in our App", "content": "", "videoLink": ""}]}, {"module": 2, "title": "Setting up <PERSON><PERSON>", "duration": 47, "lessons": [{"lesson": 2.2, "title": "A Quick Explanation of Docker", "content": "", "videoLink": ""}, {"lesson": 2.3, "title": "Installing Docker on Your Local Machine", "content": "", "videoLink": ""}, {"lesson": 2.4, "title": "Creating a Dockerfile for Our App", "content": "", "videoLink": ""}, {"lesson": 2.5, "title": "Exploring Our Docker Images and Containers", "content": "", "videoLink": ""}, {"lesson": 2.6, "title": "A Brief Look at Overwriting in Docker", "content": "", "videoLink": ""}, {"lesson": 2.7, "title": "Creating Convenient npm Scripts for Docker Commands", "content": "", "videoLink": ""}]}]}