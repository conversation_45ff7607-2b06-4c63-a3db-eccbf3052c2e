export const ActivityTypes = Object.freeze({});

export const Levels = Object.freeze({
  LEVEL_ONE: {
    pointsRange: { min: 0, max: 100 },
    privileges: 'Welcome badge, basic profile customization',
  },
  LEVEL_TWO: {
    pointsRange: { min: 101, max: 300 },
    privileges: 'Access to exclusive articles or content',
  },
  LEVEL_THREE: {
    pointsRange: { min: 301, max: 600 },
    privileges: 'Entry into monthly prize draws',
  },
  LEVEL_FOUR: {
    pointsRange: { min: 601, max: 1000 },
    privileges: 'Ability to create and moderate study groups',
  },
  LEVEL_FIVE: {
    pointsRange: { min: 1001, max: 1500 },
    privileges: 'Early access to new features',
  },
  LEVEL_SIX_PLUS: {
    pointsRange: { min: 1501, max: Infinity },
    privileges: 'Scholarships, mentorship sessions, premium content',
  },
});

export const PointsEvents = Object.freeze({
  NEW_PUBLIC_ARTIFACT: {
    ref: 'newPublicArtifact',
    name: 'New public artifact',
    description: 'New public artifact created',
    points: 20,
    multiplier: {
      three_day_streak: {
        value: 1.1,
        criteria: '3 days in a row',
      },
      seven_day_streak: {
        value: 1.2,
        criteria: '7 days in a row',
      },
    },
  },
  SHARE_PUBLIC_ARTIFACT: {
    ref: 'sharePublicArtifact',
    name: 'Share a public artifact',
    description: 'A public artifact shared',
    points: 10,
  },
  ARTIFACT_COMMENT_RECIEVED: {
    ref: 'artifactCommentRecieved',
    name: 'Artifact owner gets a new approved comment',
    description: 'New artifact comment recieved',
    points: 5,
  },
  ARTIFACT_LIKE_RECIEVED: {
    ref: 'artifactLikeRecieved',
    name: 'Artifact owner gets a new like for artifact',
    description: 'New artifact like recieved',
    points: 2,
  },
});
