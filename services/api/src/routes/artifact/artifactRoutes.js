import express from 'express';
import passport from 'passport';
import config from '../../lib/config.js';
import { ArtifactService } from '../../services/artifact/ArtifactService.js';
import { InternalError, PermissionError } from '../../errors/index.js';
import { defineAbilityFor } from '../../lib/auth/abilities/defineAbilityFor.js';

import { Actions } from '../../lib/auth/abilities/Actions.js';
import { Subjects } from '../../lib/auth/abilities/Subjects.js';

const artifactRouter = express.Router();

/**
 * Learning Artifact routes.
 *
 * @param router Express Router.
 */
export const artifactRoutes = (router) => {
  router.use('/artifact', artifactRouter);

  artifactRouter.get('/', (req, res) => {
    ArtifactService.getAll()
      .then((artifacts) => res.status(200).json(artifacts))
      .catch((err) =>
        res
          .status(500)
          .json(
            new InternalError(
              'Failed to get artifacts',
              'artifact',
              `${err.message}`
            )
          )
      );
  });

  artifactRouter.post(
    '/',
    checkPermissions(Actions.WRITE, Subjects.ARTIFACT),
    (req, res) => {
      ArtifactService.create(req.body)
        .then((artifact) => {
          const id = artifact._id;

          return res.status(200).json({
            message: `Artifact with id: ${id} deleted successfully`,
            code: 200,
          });
        })
        .catch((err) =>
          res
            .status(500)
            .json(
              new InternalError(
                `Failed to create artifact ${err.message}`,
                'artifact'
              )
            )
        );
    }
  );
};
