import express from 'express';
import passport from 'passport';
import config from '../../lib/config.js';
import { AuthService } from '../../services/auth/AuthService.js';

const authRoutes = express.Router();

export const githubAuthRoutes = (router) => {
  router.use(config.frontend.authPrefix, authRoutes);

  authRoutes.get(
    config.auth.github.route, 
    AuthService.storeRedirectUri,
    passport.authenticate('github', { scope: ['user:email'] })
  );

  authRoutes.get(
    config.auth.github.callbackPage, 
    AuthService.authenticate('github', config.auth.github.failedPage),
    AuthService.authCallBack
  );
};
