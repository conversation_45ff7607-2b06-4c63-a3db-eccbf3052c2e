import express from 'express';
import { logger } from '../../lib/util/logger.js';
import config from '../../lib/config.js';
import { googleAuthRoutes } from './googleRouter.js';

const logoutRoute = express.Router();

export default (router) => {
  router.use(config.frontend.authPrefix, logoutRoute);

  // Logout route
  /// TODO this needs some work
  logoutRoute.get('/logout', (req, res) => {
    logger.debug('Log out');
    req.logout(function (err) {
      req.session = null;
      req.cookies = null;
      res.redirect(`${config.frontend.host}/auth/sign-in`);
    });
  });

  // Google auth routes
  googleAuthRoutes(router);
};
