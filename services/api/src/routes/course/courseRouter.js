import express from 'express';
import { CourseService } from '../../services/course/CourseService.js';
import { checkPermissions } from '../middleware/checkPermissions.js';
import { validateRequest } from '../middleware/authenticatedCheck.js';
import { Actions, Subjects } from '../../lib/auth/abilities/index.js';
import { InternalError } from '../../errors/index.js';
import { logger } from '../../lib/util/logger.js';

const courseRouter = express.Router();

/**
 * Course routes.
 *
 * @param router Express Router.
 */
export const courseRoutes = (router) => {
  router.use('/course', validateRequest, courseRouter);

  courseRouter.get(
    '/',
    checkPermissions(Actions.READ, Subjects.COURSE),
    (req, res) => {
      CourseService.getAll()
        .then((courses) => res.status(200).json(courses))
        .catch((err) =>
          res
            .status(500)
            .json(
              new InternalError(
                'Failed to get courses',
                'course',
                `${err.message}`
              )
            )
        );
    }
  );

  courseRouter.get(
    '/:id',
    checkPermissions(Actions.READ, Subjects.COURSE),
    async (req, res) => {
      try {
        const courseId = req.params.id;
        const includeProgress = req.query.includeProgress === 'true';
        const userId = req.user._id;

        let course = await CourseService.get(courseId);

        if (includeProgress && userId) {
          const progress = await CourseService.getCourseProgress(
            courseId,
            userId
          );

          course = {
            ...course.toObject(),
            userProgress: progress,
          };
        }

        res.status(200).json(course);
      } catch (err) {
        res
          .status(500)
          .json(
            new InternalError(`Failed to get course: ${err.message}`, 'course')
          );
      }
    }
  );

  courseRouter.post(
    '/progress/:courseId',
    checkPermissions(Actions.UPDATE, Subjects.COURSE),
    (req, res) => {
      logger.debug('Updating course progress', req.body);
      CourseService.updateCourseProgress(
        req.params.courseId,
        req.user.id,
        req.body
      )
        .then((courseProgress) => res.status(200).json(courseProgress))
        .catch((err) =>
          res
            .status(500)
            .json(
              new InternalError(
                `Failed to update course progress for course ${req.params.courseId}: ${err.message}`,
                'course'
              )
            )
        );
    }
  );
};
