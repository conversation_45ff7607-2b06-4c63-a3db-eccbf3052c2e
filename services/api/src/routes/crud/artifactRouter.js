import express from 'express';
import { ArtifactService } from '../../services/artifact/ArtifactService.js';
import { checkPermissions } from '../middleware/checkPermissions.js';
import { validateRequest } from '../middleware/authenticatedCheck.js';
import { Actions, Subjects } from '../../lib/auth/abilities/index.js';
import { InternalError } from '../../errors/index.js';
import { eventBus } from '../../events/EventBus.js';
import { NEW_ARTIFACT_CREATED } from '../../events/eventTypes.js';

const bus = eventBus();

const artifactRouter = express.Router();

/**
 * Artifacts routes.
 *
 * @param router Express Router.
 */
export const artifactRoutes = (router) => {
  router.use('/artifacts', validateRequest, artifactRouter);

  artifactRouter.get(
    '/',
    checkPermissions(Actions.READ, Subjects.ARTIFACT),
    (req, res) => {
      ArtifactService.getAll()
        .then((artifacts) => res.status(200).json(artifacts))
        .catch((err) =>
          res
            .status(500)
            .json(
              new InternalError(
                'Failed to get artifacts',
                'artifact',
                `${err.message}`
              )
            )
        );
    }
  );

  artifactRouter.get(
    '/creator',
    checkPermissions(Actions.READ, Subjects.ARTIFACT),
    (req, res) => {
      ArtifactService.getArtifactsByCreator(req?.user?._id)
        .then((artifacts) => res.status(200).json(artifacts))
        .catch((err) =>
          res
            .status(500)
            .json(
              new InternalError(
                `Failed to POST artifacts by creator ${JSON.stringify(
                  err.message
                )}`,
                'artifact'
              )
            )
        );
    }
  );

  artifactRouter.get(
    '/:id',
    checkPermissions(Actions.READ, Subjects.ARTIFACT),
    (req, res) => {
      ArtifactService.get(req.params.id)
        .then((artifact) => res.status(200).json(artifact))
        .catch((err) =>
          res
            .status(500)
            .json(
              new InternalError(
                `Failed to get artifact ${err.message}`,
                'artifact'
              )
            )
        );
    }
  );

  artifactRouter.post(
    '/',
    checkPermissions(Actions.WRITE, Subjects.ARTIFACT),
    (req, res) => {
      ArtifactService.create(req.user._id, req.body, req.user)
        .then((artifact) => {
          if (artifact && artifact.permissions.accessLevel === 'public') {
            bus.notify(NEW_ARTIFACT_CREATED, req.user);
          }
          return res.status(200).json(artifact);
        })
        .catch((err) =>
          res
            .status(500)
            .json(
              new InternalError(
                `Failed to create artifact ${err.message}`,
                'note'
              )
            )
        );
    }
  );

  artifactRouter.delete(
    '/:id',
    checkPermissions(Actions.DELETE, Subjects.ARTIFACT),
    (req, res) => {
      ArtifactService.delete(req.params.id)
        .then((artifact) => {
          const id = artifact._id;

          return res.status(200).json({
            message: `Artifact with id: ${id} deleted successfully`,
            code: 200,
          });
        })
        .catch((err) =>
          res
            .status(500)
            .json(
              new InternalError(
                `Failed to delete artifact ${err.message}`,
                'artifact'
              )
            )
        );
    }
  );
};
