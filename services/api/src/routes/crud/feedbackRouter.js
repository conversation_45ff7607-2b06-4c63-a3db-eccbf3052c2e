import express from 'express';
import { FeedbackService } from '../../services/feedback/FeedbackService.js';
import { checkPermissions } from '../middleware/checkPermissions.js';
import { Actions, Subjects } from '../../lib/auth/abilities/index.js';
import { InternalError } from '../../errors/index.js';

const feedbackRouter = express.Router();

/**
 * Artifacts routes.
 *
 * @param router Express Router.
 */
export const feedbackRoutes = (router) => {
  router.use('/feedback', feedbackRouter);

  feedbackRouter.get('/', (req, res) => {
    FeedbackService.getAll()
      .then((feedback) => res.status(200).json(feedback))
      .catch((err) =>
        res
          .status(500)
          .json(
            new InternalError(
              'Failed to get feedback',
              'artifact',
              `${err.message}`
            )
          )
      );
  });

  feedbackRouter.get(
    '/creator',
    checkPermissions(Actions.READ, Subjects.FEEDBACK),
    (req, res) => {
      FeedbackService.getFeedbackForArtifact(req?.user?._id)
        .then((feedback) => res.status(200).json(feedback))
        .catch((err) =>
          res
            .status(500)
            .json(
              new InternalError(
                `Failed to POST feedback by creator ${err.message}`,
                'feedback'
              )
            )
        );
    }
  );

  feedbackRouter.get(
    '/:id',
    checkPermissions(Actions.READ, Subjects.FEEDBACK),
    (req, res) => {
      FeedbackService.get(req.params.id)
        .then((feedback) => res.status(200).json(feedback))
        .catch((err) =>
          res
            .status(500)
            .json(
              new InternalError(
                `Failed to get feedback ${err.message}`,
                'feedback'
              )
            )
        );
    }
  );

  feedbackRouter.post('/', (req, res) => {
    FeedbackService.create('666065f79185260b6bd09f94', req.body)
      .then((feedback) => res.status(200).json(feedback))
      .catch((err) =>
        res
          .status(500)
          .json(
            new InternalError(
              `Failed to create feedback ${err.message}`,
              'feedback'
            )
          )
      );
  });

  feedbackRouter.delete(
    '/:id',
    checkPermissions(Actions.DELETE, Subjects.FEEDBACK),
    (req, res) => {
      FeedbackService.delete(req.params.id)
        .then((feedback) => {
          const id = feedback._id;

          return res.status(200).json({
            message: `Feedback with id: ${id} deleted successfully`,
            code: 200,
          });
        })
        .catch((err) =>
          res
            .status(500)
            .json(
              new InternalError(
                `Failed to delete feedback ${err.message}`,
                'feedback'
              )
            )
        );
    }
  );
};
