import express from 'express';
import passport from 'passport';
import config from '../../lib/config.js';
import { NoteService } from '../../services/note/NoteService.js';
import { checkPermissions } from '../middleware/checkPermissions.js';
import { Actions, Subjects } from '../../lib/auth/abilities/index.js';

const noteRouter = express.Router();

/**
 * Notes routes.
 *
 * @param router Express Router.
 */
export const noteRoutes = (router) => {
  router.use('/notes', noteRouter);

  noteRouter.get('/', (req, res) => {
    NoteService.getAll()
      .then((notes) => res.status(200).json(notes))
      .catch((err) =>
        res
          .status(500)
          .json(
            new InternalError('Failed to get notes', 'note', `${err.message}`)
          )
      );
  });

  noteRouter.get(
    '/creator',
    checkPermissions(Actions.READ, Subjects.NOTE),
    (req, res) => {
      NoteService.getNotesByCreator(req.user._id)
        .then((notes) => res.status(200).json(notes))
        .catch((err) =>
          res
            .status(500)
            .json(
              new InternalError(
                `Failed to get notes by creator ${err.message}`,
                'note'
              )
            )
        );
    }
  );

  noteRouter.post('/', (req, res) => {
    NoteService.create(req.body)
      .then((artifact) => res.status(200).json(artifact))
      .catch((err) =>
        res
          .status(500)
          .json(
            new InternalError(`Failed to create note ${err.message}`, 'note')
          )
      );
  });
};
