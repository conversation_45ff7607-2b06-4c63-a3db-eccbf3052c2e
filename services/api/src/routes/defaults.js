import express from 'express';
import { logger } from '../lib/util/logger.js';

const defaultRouter = express.Router();

export const defaultRoutes = (router) => {
  const validateRequest = (req, res, next) => {
    if (!req.user) {
      logger.error(`User is unauthenticated`);
      res.sendStatus(403);
      return false;
    }
    next();
  };

  router.use('/', defaultRouter);

  defaultRouter.get('/ping', validateRequest, (req, res) => {
    console.log(`ℹ️ - Ping route: ${req.url} ${Date.now()}`);
    res.status(200).json({
      message: '✅ - Pong: test successful',
    });
  });

  // Catch all route for the ping (only allow get)
  defaultRouter.all('/ping', (req, res) => {
    const code = 405;
    return res.status(code).json({
      code,
      message: `${req.method} method not allowed for route ${req.url}`,
    });
  });

  // Catch all 404 not found route
  defaultRouter.all('*', (req, res) => {
    const code = 404;
    return res.status(code).json({
      code,
      message: `${req.url} not found`,
    });
  });
};
