import express from 'express';
import config from '../lib/config.js';

import authRoutes from './auth/index.js';
import { userRoutes } from './crud/userRouter.js';
import { defaultRoutes } from './defaults.js';
import { artifactRoutes } from './crud/artifactRouter.js';
import { noteRoutes } from './crud/noteRouter.js';
import { feedbackRoutes } from './crud/feedbackRouter.js';
import { courseRoutes } from './course/courseRouter.js';
import { proxyRoutes } from './networking/proxyRouter.js';
import { linksRoutes } from './networking/linkRouter.js';
import { streaksRoutes } from './streaks/streaksRouter.js';

export default () => {
  const router = express.Router();

  if (config.streaks.enabled) {
    streaksRoutes(router);
  }

  if (config.routes.auth) {
    authRoutes(router);
  }

  if (config.routes.user) {
    userRoutes(router);
  }

  if (config.routes.artifact) {
    artifactRoutes(router);
  }

  if (config.routes.note) {
    noteRoutes(router);
  }

  if (config.routes.feedback) {
    feedbackRoutes(router);
  }

  if (config.routes.course) {
    courseRoutes(router);
  }

  if (config.routes.proxy) {
    proxyRoutes(router);
  }

  if (config.routes.links) {
    linksRoutes(router);
  }

  // NB !! This must be the last route in the router stack
  if (config.routes.default) {
    defaultRoutes(router);
  }

  return router;
};
