import { defineAbilityFor } from '../../lib/auth/abilities/defineAbilityFor.js';
import { PermissionError } from '../../errors/index.js';

/**
 * Used to check if user is authorised to perform an action on a subject.
 *
 */
export const checkPermissions = (action, subject) => (req, res, next) => {
  if (!defineAbilityFor(req, res).can(action, subject)) {
    return res
      .status(403)
      .json(new PermissionError(req.user.role, action, subject));
  }
  next();
};
