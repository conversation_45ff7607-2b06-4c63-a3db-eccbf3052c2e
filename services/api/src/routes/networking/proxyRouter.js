import express from 'express';
import { createProxyMiddleware } from 'http-proxy-middleware';
import config from '../../lib/config.js';

const proxyRouter = express.Router();

export const proxyRoutes = (router) => {
  router.use('/services/proxy', proxyRouter);

  proxyRouter.use(
    '/',
    createProxyMiddleware({
      target: 'not-needed',
      router: (req) => req.query.url,
      changeOrigin: true,
      logLevel: config.log.logLevel,
    })
  );
};
