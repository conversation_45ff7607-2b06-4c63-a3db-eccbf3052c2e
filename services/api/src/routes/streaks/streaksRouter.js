import express from 'express';
import { UserStreaksService } from './../../services/user/UserStreaksService.js';

const streaksRouter = express.Router();

/**
 * Streaks router
 * Used for managing users streaks records
 *
 * @param {*} router
 */
export const streaksRoutes = (router) => {
  streaksRouter.use('/', router);
  router.use('/', (req, res, next) => {
    if (req.user && req.isAuthenticated()) {
      UserStreaksService.updateLoginStreak(req.user);
    }
    next();
  });
};
