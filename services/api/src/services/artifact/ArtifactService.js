import ogs from 'open-graph-scraper';
import { Artifact } from '../../models/ArtifactSchema.js';
import { Note } from '../../models/NoteSchema.js';
import { User } from '../../models/UserSchema.js';
import { logger } from '../../lib/util/logger.js';
import { PointsEvents } from '../../models/types/index.js';
import { PointsService } from '../user/PointsService.js';

export const ArtifactService = {
  internalFields: ['-_internal', '-__v'],

  get: (id) =>
    Artifact.findById(id)
      .populate({
        path: 'notes',
        model: Note,
        select: ['_id', 'title', 'content', 'created'],
      })
      .populate({
        path: 'owner',
        model: User,
        select: ['picture', 'name.first', 'name.last', 'email'],
      })
      .select(ArtifactService.internalFields),

  getAll: () => Artifact.find().select(ArtifactService.internalFields),

  getArtifactsByCreator: (creatorId) => {
    return Artifact.find({ createdBy: creatorId })
      .populate({
        path: 'owner',
        model: User,
        select: ['picture', 'name.first', 'name.last', 'email'],
      })
      .sort({ createdAt: -1 })
      .select(ArtifactService.internalFields);
  },

  create: async (userId, data, user) => {
    // TODO I can move this into the event bus/
    const url = data.url;
    if (url && !data.metadata) {
      try {
        const { error, result } = await ogs({
          url,
        });
        if (result) {
          data.metadata = result;
        } else {
          logger.debug(
            `Failed to get metadata for ${url}: ${JSON.stringify(error)}`
          );
        }
      } catch (err) {
        logger.debug(
          `Failed to get metadata for ${url}: ${JSON.stringify(err)}`
        );
      }
    }

    const artifact = await Artifact.create({
      createdBy: userId,
      updatedBy: userId,
      owner: userId,
      ...data,
    });

    return artifact;
  },

  update: async (id, data) => {
    try {
    } catch (err) {
      throw err;
    }
  },

  delete: (id) => Artifact.findByIdAndDelete(id),
};
