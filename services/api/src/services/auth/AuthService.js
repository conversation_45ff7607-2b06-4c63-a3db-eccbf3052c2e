import passport from 'passport';
import { stringify } from 'querystring';
import config from '../../lib/config.js';
import { User } from '../../models/UserSchema.js';
import { logger } from '../../lib/util/logger.js';
import { maskData } from '../../lib/util/maskData.js';

export const AuthService = {
  storeRedirectUri: (req, res, next) => {
    req.session.redirect = req.query.redirect;
    next();
  },

  authenticate: (strategy, failedPage) => (req, res, next) => {
    passport.authenticate(strategy, {
      response: res,
      failureRedirect: `${config.frontend.host}${failedPage}`,
    })(req, res, next);
  },

  authCallBack: (req, res) => {
    const {
      id,
      name: { first, last },
      email,
      picture,
      language,
      activated,
      banned,
    } = req.user;

    const userInfo = stringify({
      id,
      name: first,
      email,
      picture,
      language,
      activated,
      banned,
    });

    if (activated && !banned) {
      const redirectUrl = req.session.redirect
        ? `${config.frontend.host}${req.session.redirect}`
        : `${config.frontend.host}`;
      delete req.session.redirect;
      res.redirect(redirectUrl);
    } else if (activated && banned) {
      res.redirect(
        `${config.frontend.host}${config.frontend.signupPage}/pending`
      );
    } else {
      res.redirect(`${config.frontend.host}${config.frontend.signupPage}?${userInfo}`);
    }
  },

  validate: async ({
    httpRequest,
    provider,
    profile,
    email,
    accessToken,
    refreshToken,
    givenName,
    familyName,
    domain,
    isDomainValid,
    picture,
    done,
    language,
    locale,
    emailVerified,
    gender,
  } = {}) => {
    let currentUser = null;

    try {
      currentUser = await User.findOne({ email }).exec();
    } catch (err) {
      logger.error('Error querying for user:', err);
      return done(err, null);
    }

    if (!currentUser) {
      const userInfo = {
        email,
        gender: gender || 'undefined',
        activated: false,
        banned: false,
        name: {
          first: givenName,
          last: familyName,
        },
        language: language || 'en',
        locale: locale || 'en',
        emailVerified,
        picture,
        security: {
          tokens: {
            [provider]: {
              accessToken,
              refreshToken,
            },
          },
          emailVerified,
          profiles: {
            default: provider,
            [provider]: profile,
          },
        },
        _internal: {
          tokens: {
            [provider]: {
              accessToken,
              refreshToken,
            },
          },
          profiles: {
            default: provider,
            [provider]: profile,
          },
        },
      };

      let newUser = null;

      try {
        newUser = await User.create(userInfo);
      } catch (err) {
        logger.error('User creation failed:', err);
        return done(err, null);
      }

      if (!newUser) {
        logger.error('User.create returned null');
        return done(new Error('User creation returned null'), null);
      }

      logger.info(
        `New user created: ${JSON.stringify({
          email: newUser.email,
          id: newUser._id,
          name: newUser.name,
        })}`
      );

      return done(null, newUser);
    }

    try {
      logger.info(
        `Existing user found: ${JSON.stringify({
          email: currentUser.email,
          id: currentUser._id,
          name: currentUser.name,
        })}`
      );
      return done(null, currentUser);
    } catch (err) {
      logger.error('Error in done() for existing user:', err);
      return done(err, null);
    }
  },
};
