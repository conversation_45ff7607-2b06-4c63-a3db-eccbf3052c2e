import passport from 'passport';
import { stringify } from 'querystring';
import config from '../../lib/config.js';
import { User } from '../../models/UserSchema.js';
import { logger } from '../../lib/util/logger.js';
import { maskData } from '../../lib/util/maskData.js';

export const AuthService = {
  storeRedirectUri: (req, res, next) => {
    req.session.redirect = req.query.redirect;
    next();
  },

  authenticate: (strategy, failedPage) => (req, res, next) => {
    passport.authenticate(strategy, {
      response: res,
      failureRedirect: `${config.frontend.host}${failedPage}`,
    })(req, res, next);
  },

  authCallBack: (req, res) => {
    const {
      id,
      name: { first, last },
      email,
      picture,
      language,
      activated,
      banned,
    } = req.user;

    const userInfo = stringify({
      id,
      name: first,
      email,
      picture,
      language,
      activated,
      banned,
    });

    if (activated && !banned) {
      const redirectUrl = req.session.redirect
        ? `${config.frontend.host}${req.session.redirect}`
        : `${config.frontend.host}`;
      delete req.session.redirect;
      res.redirect(redirectUrl);
    } else if (activated && banned) {
      res.redirect(
        `${config.frontend.host}${config.frontend.signupPage}/pending`
      );
    } else {
      res.redirect(
        `${config.frontend.host}${config.frontend.signupPage}?${userInfo}`
      );
    }
  },

  validate: async ({
    httpRequest,
    provider,
    profile,
    email,
    accessToken,
    refreshToken,
    givenName,
    familyName,
    domain,
    isDomainValid,
    picture,
    done,
    language,
    locale,
    emailVerified,
    gender,
  } = {}) => {
    const currentUser = await User.findOne({ email })
      .exec()
      .catch((err) => {
        logger.error(err);
      });

    if (!currentUser) {
      // screen user

      const userInfo = {
        email,
        gender: gender ? gender : 'undefined',
        activated: false,
        banned: false, // TODO
        name: {
          first: givenName,
          last: familyName,
        },
        language: language ? language : 'en',
        locale: locale ? locale : 'en',
        emailVerified,
        picture,
        security: {
          tokens: {},
          emailVerified,
          profiles: {
            default: provider,
          },
        },
        _internal: {
          tokens: {},
          profiles: {
            default: provider,
          },
        },
      };

      userInfo.security.profiles[provider] = profile;
      userInfo.security.tokens[provider] = {
        accessToken,
        refreshToken,
      };

      const newUser = await User.create(userInfo).catch((err) =>
        logger.error(err)
      );

      if (newUser) {
        logger.info(`New user created: ${maskData(currentUser.email, 1, 7)}`);
        try {
          done(null, newUser);
        } catch (err) {
          done(new Error(err));
        }
      }
    }

    try {
      logger.info(`Existing user found: ${maskData(currentUser.email, 1, 7)}`);
      done(null, currentUser);
    } catch (err) {
      done(new Error(err));
    }
  },
};
