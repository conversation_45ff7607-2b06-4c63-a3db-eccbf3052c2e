import { Course } from '../../models/CourseSchema.js';
import { UserCourseProgress } from '../../models/UserCourseProgressSchema.js';

export const CourseService = {
  internalFields: ['-_internal', '-__v'],

  get: (id) => Course.findById(id).select(CourseService.internalFields),

  getAll: () => Course.find().select(CourseService.internalFields),

  getCourseProgress: (courseId, userId) => {
    return UserCourseProgress.findOne({ userId, courseId }).select('-__v');
  },

  updateCourseProgress: (courseId, userId, data) => {
    const { lessonId, position } = data;

    return UserCourseProgress.findOneAndUpdate(
      { userId, courseId },
      {
        $addToSet: { completedLessons: lessonId },
        $set: { lastLesson: lessonId, lastPosition: position },
      },
      { new: true, upsert: true }
    ).select('-__v');
  },
};
