import { Feedback } from '../../models/FeedbackSchema.js';
import { Note } from '../../models/NoteSchema.js';
import { User } from '../../models/UserSchema.js';
import { Artifact } from '../../models/ArtifactSchema.js';

export const FeedbackService = {
  internalFields: ['-_internal', '-__v'],

  get: (id) => Feedback.findById(id).select(FeedbackService.internalFields),

  getAll: () => Feedback.find().select(FeedbackService.internalFields),

  getFeedbackForArtifact: (artifactId) => {
    return Feedback.find({ artifactId })
      .populate({
        path: 'owner',
        model: User,
        select: ['picture', 'name.first', 'name.last'],
      })
      .sort({ createdAt: -1 })
      .select(FeedbackService.internalFields);
  },

  create: async (userId, data) => {
    data.owner = userId;
    return Feedback.create(data);
  },

  update: async (id, data) => {
    try {
    } catch (err) {
      throw err;
    }
  },

  delete: (id) => Feedback.findByIdAndDelete(id),
};
