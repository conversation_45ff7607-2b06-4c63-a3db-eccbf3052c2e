import { Note } from '../../models/NoteSchema.js';

export const NoteService = {
  internalFields: ['-_internal', '-__v'],

  get: (id) => Note.findById(id).select(NoteService.internalFields),

  getNotesByCreator: (creatorId) => {
    return Note.find({ createdBy: creatorId })
      .sort({ createdAt: -1 })
      .select(NoteService.internalFields);
  },

  getAll: () => Note.find().select(NoteService.internalFields),

  create: (data) => Note.create(data),

  update: async (id, data) => {
    try {
    } catch (err) {
      throw err;
    }
  },

  delete: (id) => Note.findByIdAndDelete(id),
};
