import { PointsEvents } from '../../models/types/index.js';
import { User } from '../../models/UserSchema.js';
import { Activity } from '../../models/ActivitySchema.js';
import { logger } from '../../lib/util/logger.js';

export const PointsService = {
  checkDailyCap: async (user, newPoints) => {
    if (!user || !user._id) {
      return true;
    }

    const today = new Date();
    const startOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    ).getTime();

    const result = await Activity.aggregate([
      {
        $match: {
          userId: user._id,
          created: { $gte: startOfDay },
        },
      },
      {
        $group: {
          _id: null,
          totalPoints: { $sum: '$points' },
        },
      },
    ]);

    const dailyPoints = result[0]?.totalPoints || 0;
    const newTotalPoints = dailyPoints + newPoints;

    return newTotalPoints >= 1000;
  },

  updatePoints: async (user, activityType, points) => {
    if (!user || !user._id) {
      throw new Error('User not present in update points function call');
    }

    const limitReached = await PointsService.checkDailyCap(user, points);

    if (limitReached) return;

    user.points.total += points;

    // check for a level up!

    try {
      await user.save();
    } catch (err) {
      logger.error(`Error updating user points: ${err}`);
      throw new Error(err);
    }

    const activity = new Activity({
      userId: user._id,
      type: activityType,
      points,
    });

    // Check for badges!

    try {
      await activity.save();
      logger.info(
        `User ${user._id} has earned ${points} points for ${activityType}`
      );
    } catch (err) {
      logger.error(`Error saving activity: ${err}`);
      throw new Error(`Error saving activity: ${err}`);
    }

    return activity;
  },
};
