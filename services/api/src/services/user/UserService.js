import { User } from '../../models/UserSchema.js';
import { logger } from '../../lib/util/logger.js';

export const UserService = {
  internalFields: ['-_internal', '-_tags', '-__v', '-_notes', '-security'],
  publicFields: [
    '_id',
    'role',
    'points',
    'name',
    'email',
    'language',
    'locale',
    'picture',
    'level',
    'streaks',
  ],

  get: (id) => User.findById(id).select(UserService.internalFields),

  me: async (id) => {
    const fourteenDaysAgo = Date.now() - 14 * 24 * 60 * 60 * 1000;

    logger.debug(fourteenDaysAgo);

    const userWithPoints = await User.aggregate([
      { $match: { _id: id } },
      {
        $lookup: {
          from: 'activities',
          let: { userId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$userId', '$$userId'] },
                    { $gte: ['$created', fourteenDaysAgo] },
                  ],
                },
              },
            },
            { $sort: { date: -1 } },
          ],
          as: 'activities',
        },
      },
      {
        $addFields: {
          'points.history': '$activities',
        },
      },
      {
        $project: {
          _id: 1,
          role: 1,
          name: 1,
          email: 1,
          language: 1,
          locale: 1,
          picture: 1,
          level: 1,
          streaks: 1,
          'points.history': 1,
          'points.total': 1,
        },
      },
    ]);

    if (userWithPoints.length === 0) {
      throw new Error('User not found');
    }

    return userWithPoints[0];
  },

  create: (data) => User.create(data),

  update: async (id, data) => {
    // TODO!!!
    // We need to ensure that certain feilds cannot be changed by the user.

    try {
      if (data.name) {
        Object.entries(data.name).forEach(([key, value]) => {
          data[`name.${key}`] = value;
        });
        delete data.name;
      }

      return await User.findByIdAndUpdate(
        id,
        { $set: data },
        { new: true }
      ).select(['name', 'email', 'language', 'locale', 'picture']);
    } catch (err) {
      throw err;
    }
  },

  delete: (id) => User.findByIdAndDelete(id),
};
