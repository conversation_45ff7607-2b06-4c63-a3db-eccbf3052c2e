import { logger } from '../../lib/util/logger.js';

export const UserStreaksService = {
  /**
   * Used to update user's login streak
   *
   * @param user User object (comes from passport)
   * @returns Promise<void>
   */
  updateLoginStreak: async (user) => {
    if (!user || !user.streaks.daily.lastUpdated) {
      logger.info(`User object not found while updating login streak`);
      return;
    }

    const now = new Date();
    const lastUpdate = new Date(user.streaks?.daily?.lastUpdated || 0);
    const diffInDays = (now - lastUpdate) / (1000 * 60 * 60 * 24);

    if (diffInDays < 1) {
      return;
    } else if (diffInDays >= 1 && diffInDays < 2) {
      logger.debug(`User is getting a streak count increment: ${diffInDays}`);
      user.streaks.daily.count += 1;
    } else {
      user.streaks.daily.count = 1;
    }

    user.streaks.daily.lastUpdated = now;

    try {
      await user.save();
    } catch (err) {
      logger.error(`Error updating users login streak: ${err}`);
    }
  },

  updateActivityStreak: async (user, activity) => {},
};
