import supertest from 'supertest';
import config from './config';

const request = supertest.agent(config.url);

describe('/', () => {
  test('POST / should return a 404', async () => {
    const response = await request.post('/');
    expect(response.status).toEqual(404);
    expect(response.body).toEqual(
      expect.objectContaining({
        code: 404,
        message: '/ not found',
      })
    );
  });

  test('GET / should return a 404', async () => {
    const response = await request.get('/');
    expect(response.status).toEqual(404);
    expect(response.body).toEqual(
      expect.objectContaining({
        code: 404,
        message: '/ not found',
      })
    );
  });

  test('PUT / should return a 404', async () => {
    const response = await request.put('/');
    expect(response.status).toEqual(404);
    expect(response.body).toEqual(
      expect.objectContaining({
        code: 404,
        message: '/ not found',
      })
    );
  });

  test('DELETE / should return a 404', async () => {
    const response = await request.del('/');
    expect(response.status).toEqual(404);
    expect(response.body).toEqual(
      expect.objectContaining({
        code: 404,
        message: '/ not found',
      })
    );
  });
});
