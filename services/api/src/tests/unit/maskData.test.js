import { maskData } from '../../lib/util/maskData';

describe('maskData', () => {
  it('masks the middle characters of a string keeping 1 char at start and end by default', () => {
    expect(maskData('abcdef')).toBe('a***f');
  });

  it('returns empty string when input is falsy', () => {
    expect(maskData(null)).toBe('');
    expect(maskData(undefined)).toBe('');
    expect(maskData('')).toBe('');
  });

  it('returns masked version when input length is less than or equal to keepStart + keepEnd', () => {
    expect(maskData('abc', 2, 2)).toBe('***');
    expect(maskData('ab', 1, 1)).toBe('**');
    expect(maskData('a', 1, 1)).toBe('*');
  });

  it('masks properly with custom keepStart and keepEnd', () => {
    expect(maskData('1234567890', 2, 3)).toBe('12***890');
    expect(maskData('helloWorld', 3, 2)).toBe('hel***ld');
  });

  it('handles edge case where keepStart + keepEnd equals string length', () => {
    expect(maskData('abcd', 2, 2)).toBe('****');
  });
});
