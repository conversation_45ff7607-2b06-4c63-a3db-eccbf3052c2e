{"name": "dev-shack-client", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.0.0", "@angular/cdk": "^18.0.3", "@angular/common": "^18.0.0", "@angular/compiler": "^18.0.0", "@angular/core": "^18.0.0", "@angular/forms": "^18.0.0", "@angular/platform-browser": "^18.0.0", "@angular/platform-browser-dynamic": "^18.0.0", "@angular/router": "^18.0.0", "@angular/youtube-player": "^18.0.3", "@tailwindcss/typography": "^0.5.13", "@types/youtube": "^0.0.50", "ngx-markdown": "^18.0.0", "rxjs": "~7.8.0", "socket.io-client": "^4.7.5", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^18.0.2", "@angular/cli": "^18.0.2", "@angular/compiler-cli": "^18.0.0", "@tailwindcss/forms": "^0.5.7", "@types/jasmine": "~5.1.0", "autoprefixer": "^10.4.19", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "typescript": "~5.4.2"}}