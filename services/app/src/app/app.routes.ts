import { Routes } from '@angular/router';
import { authGuard } from './core/guards/auth.guard';
import { roleGuard } from './core/guards/role.guard';
import { userFetched } from './core/guards/user-fetched.guard';

import { HomeComponent } from './public/home/<USER>';
import { NotesComponent } from './portal/notes/notes.component';
import { SignInComponent } from './public/auth/sign-in/sign-in.component';
import { PortalComponent } from './portal/portal.component';
import { PendingComponent } from './public/auth/pending/pending.component';
import { AuthFailComponent } from './public/auth/auth-fail/auth-fail.component';
import { DashboardComponent } from './portal/dashboard/dashboard.component';
import { ArtifactsComponent } from './portal/artifacts/artifacts.component';
import { CreateArtifactComponent } from './portal/artifacts/create-artifact/create-artifact.component';
import { ViewArtifactComponent } from './portal/artifacts/view-artifact/view-artifact.component';
import { CreateAccountComponent } from './public/onboard/create-account/create-account.component';
import { PlayerComponent } from './portal/player/player.component';
import { CoursesComponent } from './portal/courses/courses.component';
import { ProfileComponent } from './portal/profile/profile.component';
import { CoursePlayerComponent } from './portal/courses/course-player/course-player.component';

export const routes: Routes = [
  { path: 'auth/fail', component: AuthFailComponent },
  { path: 'auth/sign-in', component: SignInComponent },
  { path: 'create-account', component: CreateAccountComponent },
  { path: 'course-player/:id', component: CoursePlayerComponent },
  {
    path: 'portal',
    component: PortalComponent,
    canActivate: [authGuard, userFetched],
    children: [
      { path: 'dashboard', component: DashboardComponent },
      {
        path: 'artifacts',
        component: ArtifactsComponent,
        canActivate: [roleGuard],
        data: { requiredRole: 'basic' },
      },
      {
        path: 'artifacts/create',
        component: CreateArtifactComponent,
        canActivate: [roleGuard],
        data: { requiredRole: 'basic' },
      },
      {
        path: 'artifacts/:id',
        component: ViewArtifactComponent,
        canActivate: [roleGuard],
        data: { requiredRole: 'basic' },
      },
      {
        path: 'notes',
        component: NotesComponent,
        canActivate: [roleGuard],
        data: { requiredRole: 'premium' },
      },
      {
        path: 'player',
        component: PlayerComponent,
        canActivate: [roleGuard],
        data: { requiredRole: 'premium' },
      },
      {
        path: 'courses',
        component: CoursesComponent,
        canActivate: [roleGuard],
        data: { requiredRole: 'premium' },
      },
      {
        path: 'courses/:id',
        component: CoursePlayerComponent,
        canActivate: [roleGuard],
        data: { requiredRole: 'premium' },
      },
      {
        path: 'profile',
        component: ProfileComponent,
        canActivate: [roleGuard],
        data: { requiredRole: 'guest' },
      },
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
      // { path: 'profile', component: ProfileComponent }
    ],
  },
  { path: 'pending', component: PendingComponent },
  { path: 'home', component: HomeComponent },
  { path: '', redirectTo: 'portal', pathMatch: 'full' },
  { path: '**', redirectTo: '/home', pathMatch: 'full' },
];
