import { Directive, HostListener } from '@angular/core';
import { CommandPaletteService } from './../../core/services/command-palette.service';

@Directive({
  selector: '[appCommandPaletteTrigger]',
  standalone: true,
})
export class CommandPaletteTriggerDirective {
  constructor(private commandPaletteService: CommandPaletteService) {}

  @HostListener('window:keydown', ['$event'])
  handleKeydown(event: KeyboardEvent): void {
    if (event.metaKey && event.shiftKey && event.key === 'k') {
      debugger;
      event.preventDefault();
      this.commandPaletteService.open();
    }
  }
}
