import { inject } from '@angular/core';
import { CanActivateFn } from '@angular/router';
import { Router } from '@angular/router';
import { UserService } from '../services/user.service';

export const authGuard: CanActivateFn = () => {
  const router = inject(Router);
  const userService = inject(UserService);

  // Check for the session and session-sig cookies
  //   const sessionCookie = userService.getCookie('session');
  //   const sessionSigCookie = userService.getCookie('session.sig');

  //   if (sessionCookie && sessionSigCookie) {
  //     return true;
  //   } else {
  //     router.navigate(['/auth/sign-in']);
  //     return false;
  //   }

  return true;
};
