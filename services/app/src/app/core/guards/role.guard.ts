import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivateFn } from '@angular/router';
import { Router } from '@angular/router';
import { UserService } from '../services/user.service';
import { OverlayService } from '../services/overlay.service';

export const roleGuard: CanActivateFn = (route: ActivatedRouteSnapshot) => {
  const router = inject(Router);
  const userService = inject(UserService);

  const overlayService = inject(OverlayService);

  const userRole = userService.getUserRole();
  const requiredRole = route.data['requiredRole'];

  const rolesHierachy = ['guest', 'basic', 'premium', 'founding', 'admin'];
  const userRoleIndex = rolesHierachy.indexOf(userRole);
  const requiredRoleIndex = rolesHierachy.indexOf(requiredRole);

  if (userRoleIndex >= requiredRoleIndex) {
    console.log(
      `Users role is ${userRole} and required role is ${requiredRole}`
    );
    return true;
  } else {
    overlayService.showPermissionsError(
      `You don't have permission to access this page.`
    );
    return false;
  }

  // router.navigate(['/auth/sign-in']);

  return false;
};
