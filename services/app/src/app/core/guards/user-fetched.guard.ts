import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { UserService } from '../services/user.service';
import { Observable, of } from 'rxjs';
import { tap, catchError, map } from 'rxjs/operators';

export const userFetched: CanActivateFn = (): Observable<boolean> => {
  const userService = inject(UserService);
  const router = inject(Router);

  // Only for offline dev usage
  // return of(true);

  return userService.fetchUser().pipe(
    map((user) => !!user),
    tap((userFetched) => {
      if (!userFetched) {
        router.navigate(['/auth/sign-in']);
      }
    }),
    catchError(() => {
      router.navigate(['/auth/sign-in']);
      return of(false);
    })
  );
};
