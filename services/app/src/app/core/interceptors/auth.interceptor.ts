import { Injectable } from '@angular/core';
import {
  HttpEvent,
  HttpInterceptor,
  HttpHandler,
  HttpRequest,
  HttpResponse,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Router } from '@angular/router';
import { OverlayService } from '../services/overlay.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(private router: Router, private overlayService: OverlayService) {}

  intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 403) {
          if (error.error && error.error.type === 'permissions') {
            // Show the permissions error component
            this.overlayService.showPermissionsError(error.error.message);
          } else {
            // Navigate to sign-in page
            this.router.navigate(['auth/sign-in']);
          }
        }

        return throwError(() => new Error('Something went wrong'));
      })
    );
  }
}
