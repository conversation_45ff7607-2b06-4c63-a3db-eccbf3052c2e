export interface Artifact {
  _id?: string;
  settings?: any;
  title: string;
  description?: string;
  url: string;
  type: string;
  owner: Owner;
  identifiers?: {
    short?: string;
    perUser?: string;
  };
  tags?: string[];
  notes?: string[];
  status?: string;
  created: number;
  updated: number;
  linkedTopics?: string[]; // Array of Topic IDs
  createdBy?: string; // Should be a user ID
  updatedBy?: string; // Should be a user ID
  permissions?: {
    public?: boolean;
    sharedWith?: string[]; // Array of User IDs
    roles?: {
      viewer?: string[]; // Array of User IDs
      editor?: string[]; // Array of User IDs
      admin?: string[]; // Array of User IDs
    };
  };
  metadata?: {
    ogType: string;
    ogTitle: string;
    ogDescription: string;
    ogImage: [OgImage];
    ogSiteName: string;
    ogLocale: string;
    favicon: string;
  };
}

interface Owner {
  name: {
    first: string;
    last: string;
  };
  email: string;
  _id: string;
  picture: string;
}

interface OgImage {
  height: string;
  url: string;
  width: string;
  alt: string;
  type: string;
}

export interface ArtifactRequest {
  title: string;
  url: string;
  type: string;
  settings?: {};
  description?: string;
  tags?: string[];
  notes?: string[];
  status?: string;
  permissions?: {};
}
