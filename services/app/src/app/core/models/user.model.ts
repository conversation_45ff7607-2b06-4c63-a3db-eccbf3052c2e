export interface User {
  name: Name;
  points: Points;
  role: string;
  _id: string;
  email: string;
  language: string;
  locale: string;
  picture: string;
  streaks: Streaks;
  level: number;
}

export interface Name {
  first: string;
  last: string;
}

export interface Points {
  total: number;
  history: any[];
}

export interface Streaks {
  daily: {
    count: number;
    lastUpdated: number;
  };
  activity: {
    studySession: {
      count: number;
      lastUpdated: number;
    };
    courseProgress: {
      count: number;
      lastUpdated: number;
    };
  };
}
