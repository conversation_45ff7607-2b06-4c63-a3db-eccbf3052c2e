// src/app/core/services/api.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class ApiService {
  private apiUrl = 'http://localhost:3000/api';

  constructor(private http: HttpClient) {}

  get<T>(endpoint: string): Observable<T> {
    return this.http
      .get<T>(`${this.apiUrl}/${endpoint}`, { withCredentials: true })
      .pipe(catchError(this.handleError));
  }

  post<T>(endpoint: string, data: any): Observable<T> {
    return this.http
      .post<T>(`${this.apiUrl}/${endpoint}`, data, { withCredentials: true })
      .pipe(catchError(this.handleError));
  }
  put<T>(endpoint: string, data: any): Observable<T> {
    return this.http
      .put<T>(`${this.apiUrl}/${endpoint}`, data, { withCredentials: true })
      .pipe(catchError(this.handleError));
  }

  delete<T>(endpoint: string): Observable<T> {
    return this.http
      .delete<T>(`${this.apiUrl}/${endpoint}`, { withCredentials: true })
      .pipe(catchError(this.handleError));
  }

  ping(): Observable<any> {
    return this.get('ping');
  }

  signIn(): Observable<any> {
    return this.get('auth/google');
  }

  signOut(): Observable<any> {
    return this.get('auth/logout');
  }

  private handleError(error: HttpErrorResponse) {
    // Handle the error
    return throwError(new Error(error.message));
  }
}
