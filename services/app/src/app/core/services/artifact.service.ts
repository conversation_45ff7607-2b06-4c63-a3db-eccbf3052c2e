import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, ReplaySubject, of } from 'rxjs';

import { ApiService } from './api.service';

import { Artifact, ArtifactRequest } from '../models/artifact.model';
import { tap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class ArtifactService {
  private artifactsSubject = new BehaviorSubject<Artifact[]>([]);
  public artifacts$ = this.artifactsSubject.asObservable();

  constructor(private apiService: ApiService) {}

  getArtifacts(): void {
    this.apiService.get<Artifact[]>(`artifacts/creator`).subscribe({
      next: (data) => this.artifactsSubject.next(data),
      error: (error) => console.error('Error fetching artifacts:', error),
    });
  }

  getArtifactById(id: string): Observable<Artifact> {
    return this.apiService.get<Artifact>(`artifacts/${id}`);
  }

  createArtifact(artifact: ArtifactRequest): Observable<Artifact> {
    return this.apiService.post<Artifact>(`artifacts`, artifact);
  }

  updateArtifact(id: string, artifact: Artifact): Observable<Artifact> {
    return this.apiService.put<Artifact>(`artifacts/${id}`, artifact);
  }

  deleteArtifact(id: string): Observable<void> {
    return this.apiService
      .delete<void>(`artifacts/${id}`)
      .pipe(tap(() => this.getArtifacts()));
  }
}
