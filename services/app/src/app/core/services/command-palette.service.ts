import { Injectable, Injector } from '@angular/core';
import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import { CommandPalettesComponent } from './../../shared/components/command-palettes/command-palettes.component';

@Injectable({
  providedIn: 'root',
})
export class CommandPaletteService {
  private overlayRef: OverlayRef;

  constructor(private overlay: Overlay, private injector: Injector) {
    this.overlayRef = this.overlay.create({
      hasBackdrop: true,
      backdropClass: 'bg-gray-800 bg-opacity-75 fixed inset-0 z-4000',
      panelClass: 'z-50',
      positionStrategy: this.overlay
        .position()
        .global()
        .centerHorizontally()
        .centerVertically(),
    });

    this.overlayRef.backdropClick().subscribe(() => this.close());
  }

  open(): void {
    if (!this.overlayRef.hasAttached()) {
      const portal = new ComponentPortal(
        CommandPalettesComponent,
        null,
        this.injector
      );
      this.overlayRef.attach(portal);
    }
  }

  close(): void {
    if (this.overlayRef.hasAttached()) {
      this.overlayRef.detach();
    }
  }
}
