import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, ReplaySubject, of } from 'rxjs';

import { ApiService } from './api.service';

import { Course } from '../models/course.model';
import { map, distinctUntilChanged, catchError, tap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class CourseService {
  constructor(private apiService: ApiService) {}

  getCourses(): Observable<Course[]> {
    return this.apiService.get<Course[]>(`course`);
  }

  getCourseById(
    id: string,
    includeProgress: boolean = false
  ): Observable<Course> {
    const url = `course/${id}${includeProgress ? '?includeProgress=true' : ''}`;
    return this.apiService.get<Course>(url);
  }

  completeLesson(
    courseId: string,
    lessonId: string,
    position: number
  ): Observable<any> {
    return this.apiService.post<any>(`course/progress/${courseId}`, {
      lessonId,
      position,
    });
  }
}
