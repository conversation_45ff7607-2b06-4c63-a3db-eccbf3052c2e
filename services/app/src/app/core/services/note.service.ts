import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, ReplaySubject, of } from 'rxjs';

import { ApiService } from './api.service';

import { Note } from '../models/note.model';
import { map, distinctUntilChanged, catchError, tap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class NoteService {
  constructor(private apiService: ApiService) {}

  fecthUserNotes(): Observable<any> {
    return this.apiService.get<Note[]>(`notes/creator`);
  }
}
