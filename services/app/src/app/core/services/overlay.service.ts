import {
  Injectable,
  ComponentFactoryResolver,
  Injector,
  ApplicationRef,
} from '@angular/core';
import { PermissionsErrorComponent } from '../../shared/components/permissions-error/permissions-error.component';

@Injectable({
  providedIn: 'root',
})
export class OverlayService {
  constructor(
    private componentFactoryResolver: ComponentFactoryResolver,
    private injector: Injector,
    private appRef: ApplicationRef
  ) {}

  showPermissionsError(message: string): void {
    const componentRef = this.componentFactoryResolver
      .resolveComponentFactory(PermissionsErrorComponent)
      .create(this.injector);

    componentRef.instance.message = message;
    componentRef.instance.componentRef = componentRef;

    this.appRef.attachView(componentRef.hostView);
    const domElem = (componentRef.hostView as any).rootNodes[0] as HTMLElement;
    document.body.appendChild(domElem);
  }
}
