import { Injectable } from '@angular/core';
import { Artifact } from '../models/artifact.model';

@Injectable({
  providedIn: 'root',
})
export class StudySessionService {
  private artifact!: Artifact;

  setArtifact(artifact: any): void {
    this.artifact = artifact;
  }

  getArtifact(): any {
    return this.artifact;
  }

  startSession(): void {
    console.log('Starting study session');
  }

  isYouTubeVideo(): boolean {
    const url = this.artifact.url;
    if (url.includes('youtube.com')) {
      return true;
    } else {
      return false;
    }
  }

  getYouTubeVideoId(): string {
    const regex =
      /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?/\s]{11})/;
    const match = this.artifact.url.match(regex);
    return match ? match[1] : '';
  }
}
