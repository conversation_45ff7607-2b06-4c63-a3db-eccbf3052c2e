// src/app/theme.service.ts
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ThemeService {
  private darkMode = false;

  constructor() {
    const prefersDark = window.matchMedia(
      '(prefers-color-scheme: dark)'
    ).matches;
    this.darkMode =
      localStorage.getItem('theme') === 'dark' ||
      (!localStorage.getItem('theme') && prefersDark);
    this.updateTheme();
  }

  toggleTheme() {
    this.darkMode = !this.darkMode;
    this.updateTheme();
  }

  updateTheme() {
    if (this.darkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  }
}
