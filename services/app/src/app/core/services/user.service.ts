import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, ReplaySubject, of } from 'rxjs';

import { ApiService } from './api.service';

import { User } from '../models/user.model';
import { map, distinctUntilChanged, catchError, tap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser = this.currentUserSubject
    .asObservable()
    .pipe(distinctUntilChanged());

  constructor(private apiService: ApiService) {
    //this.refetchUser();
  }

  private refetchUser(): void {
    this.apiService
      .get<User>(`user/me`)
      .pipe(
        tap((user) => this.currentUserSubject.next(user)),
        catchError((error) => {
          console.error('Error fetching user:', error);
          return of(null); // Return null in case of error
        })
      )
      .subscribe();
  }

  fetchUser(): Observable<User | null> {
    return this.apiService.get<User>(`user/me`).pipe(
      tap((user) => this.currentUserSubject.next(user)),
      catchError((error) => {
        console.error('Error fetching user:', error);
        return of(null); // Return null in case of error
      })
    );
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  getUserRole(): string {
    return this.currentUserSubject.value?.role || '';
  }

  getUser(): Observable<User | null> {
    return this.currentUser;
  }

  updateUser(id: string, data: any): Observable<any> {
    return this.apiService.put(`user/${id}`, data);
  }

  refreshUser(): void {
    this.refetchUser();
  }
}
