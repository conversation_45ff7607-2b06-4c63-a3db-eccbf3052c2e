import { Injectable } from '@angular/core';
import { io, Socket } from 'socket.io-client';
import { Observable } from 'rxjs';
import { UserService } from './user.service';

@Injectable({
  providedIn: 'root',
})
export class WebsocketService {
  private socket: Socket;

  constructor() {
    this.socket = io('ws://localhost:3000'); // Ensure this matches your server's port

    this.socket.on('connect', () => {
      console.log('Connected to WebSocket server');
    });

    // Listen for 'server-message' event
    this.socket.on('disconnect', () => {
      console.log('Disconnected from WebSocket server');
    });
  }

  sendMessage(message: string) {
    this.socket.emit('message', message);
  }

  disconnect(): void {
    this.socket.disconnect();
  }

  onServerMessage(): Observable<string> {
    return new Observable((observer) => {
      this.socket.on('server-message', (message) => {
        observer.next(message);
      });
    });
  }

  onPointsEarned(): Observable<any> {
    return new Observable((observer) => {
      this.socket.on('points-earned', (points) => {
        observer.next(points);
      });
    });
  }
}
