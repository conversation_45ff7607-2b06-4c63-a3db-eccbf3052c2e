<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  <div
    *ngFor="let artifact of artifacts"
    class="bg-white shadow-lg rounded-lg overflow-hidden mb-4"
  >
    <div class="p-4">
      <div class="flex justify-between items-start">
        <div>
          <h2 class="text-lg font-semibold">{{ artifact.title }}</h2>
          <p class="text-gray-600 text-sm my-2">{{ artifact.description }}</p>
          <span
            class="inline-flex items-center rounded-md bg-green-50 mr-2 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20"
            >{{ artifact.status }}</span
          >
          <span
            class="inline-flex items-center rounded-md bg-blue-50 mr-2 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10"
            >{{ artifact.type }}</span
          >
        </div>
        <a
          (click)="viewArtifact(artifact)"
          class="text-blue-300 hover:text-blue-500"
        >
          <span class="material-symbols-outlined text-base">open_in_new</span>
        </a>
      </div>

      <div *ngIf="artifact.metadata">
        <hr class="border-t-1 border-gray-200 dark:border-gray-700" />
        <img
          [src]="artifact.metadata.image.url"
          [alt]="artifact.metadata.image.alt || 'Image'"
          class="mt-4"
        />
        <p class="text-gray-500 text-sm mt-2">
          {{ artifact.metadata.description }}
        </p>
      </div>
      <p class="text-gray-400 text-sm mt-5">
        <span
          *ngFor="let tag of artifact.tags"
          class="inline-flex items-center rounded-md bg-gray-50 mr-2 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-600/20"
          >{{ tag }}
        </span>
      </p>
    </div>

    <div class="px-4 py-2 bg-purple-200 flex justify-end">
      <!-- <button
        class="bg-red-500 text-white py-1 px-4 rounded mr-2"
        (click)="deleteArtifact(artifact._id!)"
      >
        Delete
      </button>
      <button
        class="bg-blue-500 text-white py-1 px-4 rounded"
        (click)="updateArtifact(artifact)"
      >
        Edit
      </button> -->
    </div>
  </div>
</div>
