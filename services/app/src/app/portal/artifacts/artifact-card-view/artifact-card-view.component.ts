import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-artifact-card-view',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './artifact-card-view.component.html',
  styleUrl: './artifact-card-view.component.css',
})
export class ArtifactCardViewComponent {
  @Input() artifacts: any[] = [];

  constructor(private router: Router) {}

  viewArtifact(artifact: any) {
    this.router.navigate(['portal/artifacts', artifact._id], {
      state: { artifact },
    });
  }
}
