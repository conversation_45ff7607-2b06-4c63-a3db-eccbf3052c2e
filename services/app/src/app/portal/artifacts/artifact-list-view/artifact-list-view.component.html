<div class="bg-white overflow-hidden">
  <ng-container *ngFor="let status of groupedArtifacts | keyvalue">
    <div class="mb-10">
      <div class="flex space-x-6 items-center">
        <span
          class="uppercase inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-sm"
          [ngClass]="getStatusClass(status.key)"
          >{{ status.key }}</span
        >
        <p class="text-sm font-medium text-gray-500">
          {{ getGroupCount(status.key) }}
        </p>
        <button
          class="text-sm font-light text-gray-600 hover:bg-green-50 hover:rounded px-2 py-1"
        >
          + Add Artifact
        </button>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full">
          <thead>
            <tr>
              <th
                class="py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 w-2/5"
              >
                Title
              </th>
              <th
                class="py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 w-1/6"
              >
                Created
              </th>
              <th
                class="py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 w-1/6"
              >
                Owner
              </th>
              <th
                class="py-3 border-b border-gray-200 text-left text-xs leading-4 font-medium text-gray-500 w-1/6"
              >
                Type
              </th>
              <th class="py-3 border-b border-gray-200 w-1/6"></th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr *ngFor="let artifact of status.value">
              <td class="py-2 whitespace-no-wrap w-2/5">
                <div class="text-sm leading-5 font-medium text-gray-900">
                  {{ artifact.title }}
                </div>
                <div class="text-xs leading-5 text-gray-500">
                  {{ artifact.description }}
                </div>
              </td>
              <td
                class="py-2 whitespace-no-wrap text-sm leading-5 text-gray-500 w-1/6"
              >
                {{ artifact.created | date }}
              </td>
              <td
                class="py-2 whitespace-no-wrap text-sm leading-5 text-gray-500 w-1/6"
              >
                <img
                  [src]="artifact?.owner?.picture"
                  class="w-8 h-8 rounded-full"
                  alt="Profile picture of owner"
                />
              </td>
              <td
                class="py-2 whitespace-no-wrap text-sm leading-5 text-gray-500 w-1/6"
              >
                {{ artifact.type }}
              </td>
              <td
                class="py-2 space-x-4 whitespace-no-wrap text-right text-sm leading-5 font-medium w-1/6"
              >
                <a
                  (click)="viewArtifact(artifact)"
                  class="text-blue-300 hover:text-blue-500 cursor-pointer"
                >
                  <span class="material-symbols-outlined text-base">edit</span>
                </a>
                <a
                  (click)="openDeleteConfirmation(artifact)"
                  class="text-blue-300 hover:text-blue-500 cursor-pointer"
                >
                  <span class="material-symbols-outlined text-base"
                    >delete</span
                  >
                </a>
                <a
                  (click)="viewArtifact(artifact)"
                  class="text-blue-300 hover:text-blue-500 cursor-pointer"
                >
                  <span class="material-symbols-outlined text-base"
                    >open_in_new</span
                  >
                </a>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </ng-container>
  <div
    *ngIf="showConfirmationDialog"
    class="fixed inset-0 flex z-10 items-center justify-center bg-gray-800 bg-opacity-75"
  >
    <div class="bg-white p-6 rounded shadow-md">
      <h2 class="text-xl font-semibold mb-4">Confirm Delete</h2>
      <p class="">Are you sure you want to delete this artifact?</p>
      <p class="text-md font-bold my-5">{{ artifactToDelete.title }}</p>
      <div class="flex justify-end space-x-4">
        <button
          (click)="confirmDelete()"
          class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-700"
        >
          Delete
        </button>
        <button
          (click)="cancelDelete()"
          class="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400"
        >
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>
