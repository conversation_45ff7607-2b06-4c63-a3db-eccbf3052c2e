import { Component, Input, SimpleChanges } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Artifact } from '../../../core/models/artifact.model';
import { ArtifactService } from '../../../core/services/artifact.service';

@Component({
  selector: 'app-artifact-list-view',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './artifact-list-view.component.html',
  styleUrl: './artifact-list-view.component.css',
})
export class ArtifactListViewComponent {
  @Input() artifacts: any[] = [];

  groupedArtifacts: { [key: string]: Artifact[] } = {};
  showConfirmationDialog = false;
  artifactToDelete: any;

  constructor(
    private router: Router,
    private artifactService: ArtifactService
  ) {}

  ngOnInit() {
    this.groupByStatus();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes) {
      this.groupByStatus();
    }
  }

  groupByStatus() {
    this.groupedArtifacts = this.artifacts.reduce((groups, artifact) => {
      const { status } = artifact;
      if (!groups[status]) {
        groups[status] = [];
      }
      groups[status].push(artifact);
      return groups;
    }, {});
  }

  getGroupCount(status: string): number {
    return this.groupedArtifacts[status].length;
  }

  viewArtifact(artifact: any) {
    this.router.navigate(['portal/artifacts', artifact._id], {
      state: { artifact },
    });
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'in progress':
        return 'bg-blue-50 text-blue-700 ring-blue-700/10';
      case 'not started':
        return 'bg-yellow-50 text-yellow-700 ring-yellow-700/10';
      case 'completed':
        return 'bg-green-50 text-green-700 ring-green-700/10';
      default:
        return 'bg-gray-50 text-gray-700 ring-gray-700/10';
    }
  }

  openDeleteConfirmation(artifact: any): void {
    this.artifactToDelete = artifact;
    this.showConfirmationDialog = true;
  }

  confirmDelete(): void {
    this.artifactService
      .deleteArtifact(this.artifactToDelete._id)
      .subscribe(() => {
        this.showConfirmationDialog = false;
        this.artifactToDelete = null;
      });
  }

  cancelDelete(): void {
    this.showConfirmationDialog = false;
    this.artifactToDelete = null;
  }
}
