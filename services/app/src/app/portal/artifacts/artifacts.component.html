<div class="container mx-auto">
  <div class="flex justify-between items-center mb-8">
    <div>
      <h1 class="text-2xl font-bold mb-2">Artifacts</h1>
      <p class="text-md font-light text-gray-600 w-5/5">
        Store any learning resource you find and come across. Each resource is
        called an Artifact.
      </p>
    </div>
    <button
      class="bg-slate-800 text-white px-4 py-2 rounded hover:bg-slate-400"
      [routerLink]="['/portal/artifacts/create']"
    >
      New Artifact
    </button>
  </div>
  <div
    class="bg-white rounded-md border-t border-b py-3 mb-10 flex justify-between items-center"
  >
    <div class="flex items-center">
      <button
        (click)="toggleView('list')"
        class="mr-2 flex items-center text-md mr-5 py-1"
        [ngClass]="{ 'border-b-2 border-slate-500': view === 'list' }"
      >
        <span class="material-symbols-outlined text-md mr-1"> list </span>List
      </button>
      <button
        (click)="toggleView('card')"
        class="flex items-center text-md"
        [ngClass]="{ 'border-b-2 border-slate-500': view === 'card' }"
      >
        <span class="material-symbols-outlined text-base mr-1"> dashboard </span
        >Card
      </button>
    </div>

    <div>
      <label for="filter" class="mr-2">Filter By:</label>
      <select id="filter" (change)="applyFilter($event)">
        <option value="">All</option>
        <option value="completed">Completed</option>
        <option value="in progress">In Progress</option>
        <option value="not started">Not Started</option>
      </select>
    </div>
    <div>
      <label for="sort" class="mr-2">Sort by:</label>
      <select id="sort" (change)="applySort($event)">
        <option value="date">Date</option>
        <option value="title">Title</option>
        <option value="type">Type</option>
      </select>
    </div>
  </div>
  <ng-container *ngIf="view === 'list' && dataLoaded">
    <app-artifact-list-view
      [artifacts]="filteredArtifacts"
    ></app-artifact-list-view>
  </ng-container>
  <ng-container *ngIf="view === 'card'">
    <app-artifact-card-view
      [artifacts]="filteredArtifacts"
    ></app-artifact-card-view>
  </ng-container>
</div>
