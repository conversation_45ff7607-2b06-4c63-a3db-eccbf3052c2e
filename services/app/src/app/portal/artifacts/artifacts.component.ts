import { Component } from '@angular/core';
import { ArtifactService } from '../../core/services/artifact.service';
import { Artifact } from '../../core/models/artifact.model';
import { CommonModule } from '@angular/common';
import { RouterLink, Router } from '@angular/router';
import { ArtifactCardViewComponent } from './artifact-card-view/artifact-card-view.component';
import { ArtifactListViewComponent } from './artifact-list-view/artifact-list-view.component';
@Component({
  selector: 'app-artifacts',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    ArtifactCardViewComponent,
    ArtifactListViewComponent,
  ],
  templateUrl: './artifacts.component.html',
  styleUrl: './artifacts.component.css',
})
export class ArtifactsComponent {
  artifacts: Artifact[] = [];
  filteredArtifacts: Artifact[] = [];
  view: 'list' | 'card' = 'list'; // Default view
  dataLoaded: boolean = false;

  constructor(
    private artifactService: ArtifactService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadArtifacts();
  }

  loadArtifacts(): void {
    this.artifactService.getArtifacts();
    this.artifactService.artifacts$.subscribe((data: Artifact[]) => {
      this.artifacts = [...data];
      this.filteredArtifacts = [...data];
      this.dataLoaded = true;
    });
  }

  viewArtifact(artifact: any) {
    this.router.navigate(['portal/artifacts', artifact._id], {
      state: { artifact },
    });
  }

  toggleView(view: 'list' | 'card'): void {
    this.view = view;
  }

  applyFilter(event: Event): void {
    const filter = (event.target as HTMLSelectElement).value;
    if (filter) {
      this.filteredArtifacts = this.artifacts.filter(
        (artifact) => artifact.status === filter
      );
    } else {
      this.filteredArtifacts = this.artifacts;
    }
  }

  applySort(event: Event): void {
    const sortBy = (event.target as HTMLSelectElement).value;
    if (sortBy === 'date') {
      this.filteredArtifacts.sort((a, b) => b.created - a.created);
    } else if (sortBy === 'title') {
      this.filteredArtifacts.sort((a, b) => a.title.localeCompare(b.title));
    } else if (sortBy === 'type') {
      this.filteredArtifacts.sort((a, b) => a.type.localeCompare(b.type));
    }
  }
}
