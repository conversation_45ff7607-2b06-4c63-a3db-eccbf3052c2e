<div class="flex">
  <div class="w-3/5">
    <div class="max-w-xl mx-auto">
      <h2 class="text-lg font-semibold mb-4">Artifact Details</h2>
      <div>
        <label class="block text-gray-700 font-semibold mb-1">Url</label>
        <input
          type="url"
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm mb-4"
          [(ngModel)]="artifact.url"
          (blur)="onBlurUrl()"
          name="url"
          placeholder="Paste the url of the resource here..."
          required
        />
      </div>
      <form (ngSubmit)="onSubmit()" #artifactForm="ngForm" class="space-y-4">
        <div>
          <label class="block text-gray-700 font-semibold">Name</label>
          <input
            type="text"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
            [(ngModel)]="artifact.title"
            name="title"
            placeholder="Give your artifact a name..."
            required
          />
        </div>
        <div>
          <label class="block text-gray-700 font-semibold">Description</label>
          <textarea
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
            [(ngModel)]="artifact.description"
            name="description"
            required
          ></textarea>
        </div>
        <div class="flex space-x-4">
          <div class="flex-1">
            <label class="block text-gray-700 font-semibold">Type</label>
            <select
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
              [(ngModel)]="artifact.type"
              name="type"
              required
            >
              <option value="blog">Blog</option>
              <option value="video">Video</option>
              <option value="ebook">Ebook</option>
              <option value="docs">Docs</option>
              <option value="tweet">Tweet</option>
              <option value="article">Article</option>
              <option value="package">Package</option>
              <option value="other">Other</option>
            </select>
          </div>
          <div class="flex-1">
            <label class="block text-gray-700 font-semibold">Status</label>
            <select
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
              [(ngModel)]="artifact.status"
              name="status"
              required
            >
              <option value="not started">Not Started</option>
              <option value="in progress">In Progress</option>
              <option value="completed">Completed</option>
            </select>
          </div>
        </div>

        <div>
          <label class="block text-gray-700 font-semibold"
            >Tags (comma separated)</label
          >
          <input
            type="text"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
            [(ngModel)]="tagsInput"
            (keyup)="onTagsChange($event)"
            name="tags"
          />
          <div class="flex flex-wrap mt-2">
            <div *ngFor="let tag of artifact.tags" class="mr-2 mb-2">
              <span
                class="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 text-gray-700 ring-1 ring-inset ring-gray-600/10"
              >
                {{ tag }}
              </span>
            </div>
          </div>
        </div>

        <div class="flex items-center my-4">
          <input
            type="checkbox"
            class="mr-2 rounded"
            [(ngModel)]="artifact.settings.favorite"
            name="favorite"
          />
          <label class="block text-gray-700">Favorite</label>
        </div>

        <div>
          <h2 class="text-lg font-semibold">Permissions</h2>
          <p class="text-sm text-gray-600">
            Manage the permissions related to your artifact. Select from the
            options below...
          </p>
          <div class="pt-5">
            <div class="space-y-4">
              <!-- Public Access Option -->
              <div class="flex items-start">
                <input
                  type="radio"
                  id="public"
                  name="privacy"
                  class="mt-1.5 mr-2 text-blue-600 focus:ring-blue-500"
                  [(ngModel)]="artifact.permissions.accessLevel"
                  value="public"
                />
                <div>
                  <label for="public" class="text-sm font-medium text-gray-900"
                    >Public access</label
                  >
                  <p class="text-sm text-gray-500">
                    Everyone with the link will see this project
                  </p>
                </div>
              </div>
              <!-- Private to Squad Members Option -->
              <div class="flex items-start">
                <input
                  type="radio"
                  id="private-squad"
                  name="privacy"
                  class="mt-1.5 mr-2 text-blue-600 focus:ring-blue-500"
                  [(ngModel)]="artifact.permissions.accessLevel"
                  value="squad"
                />
                <div>
                  <label
                    for="private-squad"
                    class="text-sm font-medium text-gray-900"
                    >Private to Squad Members</label
                  >
                  <p class="text-sm text-gray-500">
                    Only members of your squad would be able to access
                  </p>
                </div>
              </div>
              <!-- Private to You Option -->
              <div class="flex items-start">
                <input
                  type="radio"
                  id="private-you"
                  name="privacy"
                  class="mt-1.5 mr-2 text-blue-600 focus:ring-blue-500"
                  [(ngModel)]="artifact.permissions.accessLevel"
                  value="private"
                />
                <div>
                  <label
                    for="private-you"
                    class="text-sm font-medium text-gray-900"
                    >Private to you</label
                  >
                  <p class="text-sm text-gray-500">
                    You are the only one able to access this project
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <hr />
        <div class="flex justify-end space-x-4">
          <button (click)="goBack()" class="text-black px-4 py-2 rounded">
            Cancel
          </button>
          <button
            type="submit"
            class="bg-slate-900 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Create Artifact
          </button>
        </div>
      </form>
    </div>
  </div>
  <div class="w-1/3 mx-auto my-6 p-4 border rounded-lg shadow-sm bg-white">
    <div *ngIf="loading" class="flex justify-center items-center">
      <svg
        class="animate-spin h-10 w-10 text-blue-600"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 2.689 1.053 5.134 2.771 7.031l1.229-1.74z"
        ></path>
      </svg>
    </div>

    <div *ngIf="artifact?.metadata && scraped">
      <img
        [src]="artifact.metadata.image.url"
        class="rounded w-full mb-4"
        alt="{{ artifact.metadata.title }}"
      />
      <p class="text-gray-500 text-lg font-semibold">
        {{ artifact.metadata.title }}
      </p>
      <p class="text-gray-800 mt-2 text-sm">
        Source: <span class="font-medium">{{ artifact.metadata.site }}</span>
      </p>
    </div>
  </div>
</div>
