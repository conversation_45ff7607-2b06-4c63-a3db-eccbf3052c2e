import { Router } from '@angular/router';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ArtifactService } from '../../../core/services/artifact.service';
import { CommonModule, Location } from '@angular/common';

@Component({
  selector: 'app-create-artifact',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './create-artifact.component.html',
  styleUrl: './create-artifact.component.css',
})
export class CreateArtifactComponent {
  artifact = {
    settings: {
      favorite: false,
    },
    title: '',
    description: '',
    url: '',
    type: 'other',
    tags: [] as string[],
    notes: [],
    status: 'not started',
    permissions: {
      accessLevel: '',
    },
    metadata: {
      title: '',
      description: '',
      image: {
        url: '',
      },
      site: '',
    },
  };

  tagsInput = '';

  private worker!: Worker;
  public loading: boolean = false;
  public scraped: boolean = false;

  constructor(
    private artifactService: ArtifactService,
    private router: Router,
    private location: Location
  ) {}

  ngOnInit() {
    if (typeof Worker !== 'undefined') {
      this.worker = new Worker(new URL('./scraper.worker', import.meta.url));
      this.worker.onmessage = ({ data }) => {
        if (data.error) {
          console.error(data.error);
        } else {
          this.artifact.metadata = data;
          this.loading = false;
          this.scraped = true;
        }
      };
    }
  }

  onTagsChange(event: KeyboardEvent): void {
    const keys = [',', 'Enter', 'Tab'];
    if (keys.includes(event.key)) {
      const tags = this.tagsInput
        .split(',')
        .map((tag) => tag.trim())
        .filter((tag) => tag);
      tags.forEach((tag) => {
        if (!this.artifact.tags.includes(tag)) {
          this.artifact.tags.push(tag);
        }
      });
      this.tagsInput = '';
    }
  }

  onBlurUrl() {
    if (this.worker && this.artifact.url) {
      this.loading = true;
      this.worker.postMessage(this.artifact.url);
    }
  }

  onSubmit() {
    this.artifactService.createArtifact(this.artifact).subscribe({
      next: (response) => console.log('Artifact created:', response),
      error: (err) => console.error('Error creating artifact:', err),
      complete: () => this.router.navigate(['/portal/artifacts']),
    });
  }

  goBack(): void {
    this.location.back();
  }
}
