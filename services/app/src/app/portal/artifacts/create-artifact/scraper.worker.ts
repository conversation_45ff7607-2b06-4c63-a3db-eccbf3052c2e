/// <reference lib="webworker" />

addEventListener('message', ({ data }) => {
  if (isValidUrl(data)) {
    // TODO - change to env variables for url
    fetch(
      `http://localhost:3000/api/services/scrape?url=${encodeURIComponent(
        data
      )}`
    )
      .then((response) => response.json())
      .then((metadata) => {
        postMessage(metadata);
      })
      .catch((error) => {
        postMessage({ error: 'Failed to fetch metadata' });
      });
  } else {
    postMessage({ error: 'Invalid URL' });
  }
});

/**
 * Function to validate if a string is a valid URL
 * @param {string} str
 * @returns {boolean}
 */
function isValidUrl(str: string): boolean {
  try {
    new URL(str);
    return true;
  } catch (e) {
    return false;
  }
}
