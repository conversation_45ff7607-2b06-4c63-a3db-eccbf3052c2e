<div class="flex">
  <div class="flex-none w-3/5">
    <div class="">
      <div class="border-b border-gray-200">
        <nav class="flex -mb-px space-x-8" aria-label="Tabs">
          <a
            class="text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
            [ngClass]="{
              'border-blue-500 text-blue-600': selectedTab === 'info'
            }"
            (click)="selectTab('info')"
          >
            Info
          </a>
          <a
            class="text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
            [ngClass]="{
              'border-blue-500 text-blue-600': selectedTab === 'activity'
            }"
            (click)="selectTab('activity')"
          >
            Activity
          </a>
          <a
            class="text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
            [ngClass]="{
              'border-blue-500 text-blue-600': selectedTab === 'privacy'
            }"
            (click)="selectTab('privacy')"
          >
            Privacy
          </a>
          <a
            class="text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
            [ngClass]="{
              'border-blue-500 text-blue-600': selectedTab === 'notes'
            }"
            (click)="selectTab('notes')"
          >
            Notes
          </a>
          <a
            class="text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
            [ngClass]="{
              'border-blue-500 text-blue-600': selectedTab === 'topics'
            }"
            (click)="selectTab('topics')"
          >
            Topics
          </a>
          <a
            class="text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
            [ngClass]="{
              'border-blue-500 text-blue-600': selectedTab === 'metadata'
            }"
            (click)="selectTab('metadata')"
          >
            Metadata
          </a>
        </nav>
      </div>

      <!-- Tab Content -->
      <div *ngIf="selectedTab === 'info'">
        <!-- Info Content Here -->
        <h3 class="text-lg font-semibold mt-8">Title</h3>
        <h2 class="text-sm text-gray-500">
          {{ artifact?.title }}
        </h2>
        <p class="text-lg font-semibold mt-4">Description</p>
        <p class="text-sm text-gray-500">
          {{ artifact?.description }}
        </p>
      </div>
      <div *ngIf="selectedTab === 'activity'">
        <!-- Activity Content Here -->
      </div>
      <div *ngIf="selectedTab === 'privacy'">
        <!-- Privacy Content Here -->
        <div class="py-8">
          <div class="space-y-4">
            <!-- Public Access Option -->
            <div class="flex items-start">
              <input
                type="radio"
                id="public"
                name="privacy"
                class="mt-1.5 mr-2 text-blue-600 focus:ring-blue-500"
                [checked]="artifact.permissions.accessLevel === 'public'"
                disabled
              />
              <div>
                <label for="public" class="text-sm font-medium text-gray-900"
                  >Public access</label
                >
                <p class="text-sm text-gray-500">
                  Everyone with the link will see this project
                </p>
              </div>
            </div>
            <!-- Private to Squad Members Option -->
            <div class="flex items-start">
              <input
                type="radio"
                id="private-squad"
                name="privacy"
                class="mt-1.5 mr-2 text-blue-600 focus:ring-blue-500"
                [checked]="artifact.permissions.accessLevel === 'squad'"
                disabled
              />
              <div>
                <label
                  for="private-squad"
                  class="text-sm font-medium text-gray-900"
                  >Private to Squad Members</label
                >
                <p class="text-sm text-gray-500">
                  Only members of your squad would be able to access
                </p>
              </div>
            </div>
            <!-- Private to You Option -->
            <div class="flex items-start">
              <input
                type="radio"
                id="private-you"
                name="privacy"
                class="mt-1.5 mr-2 text-blue-600 focus:ring-blue-500"
                [checked]="artifact.permissions.accessLevel === 'private'"
                disabled
              />
              <div>
                <label
                  for="private-you"
                  class="text-sm font-medium text-gray-900"
                  >Private to you</label
                >
                <p class="text-sm text-gray-500">
                  You are the only one able to access this project
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div *ngIf="selectedTab === 'notes'">
        <!-- Notes Content Here -->
        <table class="min-w-full bg-white">
          <thead>
            <tr>
              <th class="py-2 px-4 border-b-2 border-gray-300 text-left">
                Note Title
              </th>
              <th class="py-2 px-4 border-b-2 border-gray-300 text-left">
                Content
              </th>
              <th class="py-2 px-4 border-b-2 border-gray-300 text-left">
                Date
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let note of artifact.notes" class="hover:bg-gray-100">
              <td class="py-4 px-4 border-b border-gray-300">
                <div>
                  <p class="font-bold text-gray-800">{{ note.title }}</p>
                </div>
              </td>
              <td class="py-4 px-4 border-b border-gray-300">
                <p class="text-gray-500">{{ note.content }}</p>
              </td>
              <td class="py-4 px-4 border-b border-gray-300">
                {{ note.date | date : "mediumDate" }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div *ngIf="selectedTab === 'topics'">
        <!-- Topics Content Here -->
      </div>
      <div *ngIf="selectedTab === 'metadata'">
        <div class="mt-8">
          <h3 class="text-lg font-semibold mb-4">Content Creator Details</h3>
          <div class="space-y-4">
            <div class="flex items-start">
              <label
                class="text-sm font-medium text-gray-900 w-32 flex-shrink-0"
                >Title:</label
              >
              <p class="text-sm text-gray-500 flex-grow">
                {{ artifact.metadata.title }}
              </p>
            </div>
            <div class="flex items-start">
              <label
                class="text-sm font-medium text-gray-900 w-32 flex-shrink-0"
                >Description:</label
              >
              <p class="text-sm text-gray-500 flex-grow">
                {{ artifact.metadata.description }}
              </p>
            </div>
            <div class="flex items-start">
              <label
                class="text-sm font-medium text-gray-900 w-32 flex-shrink-0"
                >Creator:</label
              >
              <p class="text-sm text-gray-500 flex-grow">
                {{ artifact.metadata.jsonLD[0].itemListElement[0].item.name }}
              </p>
            </div>
            <div class="flex items-start">
              <label
                class="text-sm font-medium text-gray-900 w-32 flex-shrink-0"
                >Video URL:</label
              >
              <a
                href="{{ artifact.metadata.ogUrl }}"
                class="text-blue-500 flex-grow"
                >{{ artifact.metadata.video.url }}</a
              >
            </div>
            <div class="flex items-start">
              <label
                class="text-sm font-medium text-gray-900 w-32 flex-shrink-0"
                >Published Date:</label
              >
              <p class="text-sm text-gray-500 flex-grow">
                {{ artifact.metadata.ogDate }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- <button
        class="bg-gray-800 text-white text-sm px-4 py-2 rounded hover:bg-slate-400"
        (click)="startStudySession()"
      >
        Start Study Session
      </button> -->
    </div>
  </div>
  <div class="flex-initial w-2/5">
    <div class="container mx-auto px-4">
      <div *ngIf="artifact?.metadata">
        <img
          [src]="artifact?.metadata?.image.url"
          [alt]="artifact?.metadata?.image?.alt || 'Image'"
          class="rounded mb-2"
        />
        <p class="text-gray-500 text-sm mt-2">
          {{ artifact?.metadata?.title }}
        </p>
      </div>
      <p class="text-base text-gray-700 mb-2 mt-6">Information</p>
      <hr />
      <p class="py-2 text-sm font-medium text-gray-600">
        Uploaded by: {{ artifact.owner.name?.first }}
        {{ artifact.owner.name.last }}
      </p>
      <hr />
      <p class="py-2 text-sm font-medium text-gray-600">
        Created: {{ artifact.created | date }}
      </p>
      <hr />
      <p class="py-2 text-sm font-medium text-gray-600">
        Last modified: {{ artifact.updated | date }}
      </p>
      <hr />
      <p class="py-2 text-sm font-medium text-gray-600">
        Tags: {{ artifact.tags }}
      </p>
      <hr />
    </div>
  </div>
  <!-- <div class="flex-initial w-1/4 ..."></div> -->
</div>

<!-- <div class="max-w-xl mx-auto mt-10" *ngIf="artifact">
  <h2 class="text-2xl font-bold mb-4">{{ artifact?.title }}</h2>
  <p class="text-md font-light text-gray-600 mb-6">
    {{ artifact?.description }}
  </p>
  <div *ngIf="artifact?.metadata">
    <img
      [src]="artifact?.metadata?.ogImage[0]?.url"
      [alt]="artifact?.metadata?.ogImage[0]?.alt || 'Image'"
      class="mt-4"
    />
    <p class="text-gray-500 text-sm mt-2">
      {{ artifact?.metadata?.ogDescription }}
    </p>
  </div>
  <p class="text-gray-400 text-sm mt-2">Type: {{ artifact?.type }}</p>
  <p class="text-gray-400 text-sm">Status: {{ artifact?.status }}</p>
  <p class="text-gray-400 text-sm">
    Tags: <span *ngFor="let tag of artifact?.tags">{{ tag }} </span>
  </p>
</div> -->
