import { Component } from '@angular/core';
import { ActivatedRoute, RouterLink, Router } from '@angular/router';
import { Artifact } from '../../../core/models/artifact.model';
import { CommonModule } from '@angular/common';
import { ArtifactService } from '../../../core/services/artifact.service';
import { StudySessionService } from '../../../core/services/study-session.service';

@Component({
  selector: 'app-view-artifact',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './view-artifact.component.html',
  styleUrl: './view-artifact.component.css',
})
export class ViewArtifactComponent {
  artifact: any;

  selectedTab: string = 'info';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private artifactService: ArtifactService,
    private studySessionService: StudySessionService
  ) {}

  ngOnInit(): void {
    if (!this.artifact) {
      const id = this.route.snapshot.paramMap.get('id');
      this.fetchArtifact(id); // Optional: Fallback to fetch data if not passed in state
    }
    this.navigateToTab();
  }

  navigateToTab(): void {
    this.route.queryParams.subscribe((params) => {
      this.selectedTab = params['tab'] || 'info';
    });
  }

  fetchArtifact(id: string | null): void {
    if (id) {
      this.artifactService.getArtifactById(id).subscribe({
        next: (artifact) => {
          this.artifact = artifact;
        },
        error: (err) => {
          console.error('Error creating artifact:', err);
          // Handle error
        },
        complete: () => {
          console.log('Artifact creation process completed');
        },
      });
    }
  }

  startStudySession(): void {
    if (this.artifact) {
      this.studySessionService.setArtifact(this.artifact);
      this.router.navigate(['/portal/player']);
    }
  }

  selectTab(tab: string) {
    this.selectedTab = tab;
    this.router.navigate([], {
      queryParams: { tab },
      queryParamsHandling: 'merge',
    });
  }
}
