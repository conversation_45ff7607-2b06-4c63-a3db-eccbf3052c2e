<div class="flex flex-col h-screen">
  <app-player-top-nav
    [course]="course"
    [progress]="userCourseProgress.progress"
    class="sticky top-0 z-10 w-full"
  ></app-player-top-nav>

  <div class="flex flex-grow overflow-hidden">
    <div class="border-r-2 p-4 w-80 overflow-y-auto">
      <ul class="mt-4">
        <li *ngFor="let module of course?.modules; let i = index" class="mb-4">
          <div
            (click)="toggleModule(i)"
            class="cursor-pointer font-bold mb-5 flex justify-between items-center"
          >
            {{ module.title }}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 transition-transform"
              [ngClass]="{ 'transform rotate-180': activeModuleIndex === i }"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M5.23 7.21a.75.75 0 011.06-.02L10 10.673l3.71-3.485a.75.75 0 011.04 1.08l-4 3.75a.75.75 0 01-1.04 0l-4-3.75a.75.75 0 01-.02-1.06z"
                clip-rule="evenodd"
              />
            </svg>
          </div>

          <ul *ngIf="activeModuleIndex === i" class="">
            <li *ngFor="let lesson of module.lessons" class="my-1 mr-2">
              <div class="flex items-start py-1">
                <span
                  class="material-symbols-outlined text-base mr-3"
                  [ngClass]="
                    isLessonCompleted(lesson._id)
                      ? 'text-green-500'
                      : 'text-gray-500'
                  "
                >
                  {{
                    isLessonCompleted(lesson._id)
                      ? "radio_button_checked"
                      : "radio_button_unchecked"
                  }}
                </span>
                <div>
                  <label
                    (click)="selectLesson(lesson)"
                    class="text-base text-gray-700"
                    >{{ lesson.title }}</label
                  >
                </div>
              </div>
            </li>
          </ul>
        </li>
      </ul>
    </div>

    <div class="flex-1 items-center p-8 overflow-y-auto">
      <h1 class="text-lg mb-2">{{ selectedLesson?.title }}</h1>
      <button (click)="completeLesson(selectedLesson?._id, 123)">
        Mark as Complete
      </button>
      <div class="w-4/5">
        <div
          style="padding: 56.25% 0 0 0; position: relative"
          *ngIf="selectedLesson"
        >
          <iframe
            [src]="selectedLesson.videoLink | safeUrl"
            frameborder="0"
            allow="autoplay; fullscreen; picture-in-picture; clipboard-write"
            style="
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
            "
            [title]="selectedLesson.title"
          ></iframe>
        </div>
      </div>

      <div *ngIf="selectedLesson" class="mt-6">
        <p class="mt-2">{{ selectedLesson.content }}</p>

        <div class="border-b border-gray-200">
          <nav class="flex -mb-px space-x-8" aria-label="Tabs">
            <a
              class="text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
              [ngClass]="{
                'border-blue-500 text-blue-600': selectedTab === 'details'
              }"
              (click)="selectTab('details')"
            >
              Content
            </a>
            <a
              class="text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
              [ngClass]="{
                'border-blue-500 text-blue-600': selectedTab === 'comments'
              }"
              (click)="selectTab('comments')"
            >
              Questions
            </a>
            <a
              class="text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
              [ngClass]="{
                'border-blue-500 text-blue-600': selectedTab === 'notes'
              }"
              (click)="selectTab('notes')"
            >
              Notes
            </a>
          </nav>
        </div>
      </div>
    </div>
  </div>
</div>
<script src="https://player.vimeo.com/api/player.js"></script>
<script>
  // JavaScript function to get the current position of the video
  function getCurrentVideoPosition() {
    // Get the first video element on the page
    const videoElement = document.getElementsByTagName("video")[0];
    if (videoElement) {
      return videoElement.currentTime;
    } else {
      return 0; // Return 0 if no video element is found
    }
  }
</script>
