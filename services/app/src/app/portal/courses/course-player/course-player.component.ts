import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { TopNavComponent } from '../../../shared/components/top-nav/top-nav.component';
import { CourseService } from '../../../core/services/course.service';
import { CommonModule } from '@angular/common';
import { SafeUrlPipe } from '../../../core/pipes/safe-url.pipe';
import { PlayerTopNavComponent } from '../../../shared/components/player-top-nav/player-top-nav.component';
import {
  Course,
  Lesson,
  UserCourseProgress,
} from '../../../core/models/course.model';

@Component({
  selector: 'app-course-player',
  standalone: true,
  imports: [TopNavComponent, CommonModule, SafeUrlPipe, PlayerTopNavComponent],
  templateUrl: './course-player.component.html',
  styleUrls: ['./course-player.component.css'],
})
export class CoursePlayerComponent implements OnInit {
  user: any;
  course!: Course;
  selectedLesson: Lesson | null = null;
  activeModuleIndex: number | null = 0;
  selectedTab: string = 'details';
  userCourseProgress!: UserCourseProgress;

  constructor(
    private route: ActivatedRoute,
    private courseService: CourseService
  ) {}

  ngOnInit(): void {
    const courseId = this.route.snapshot.paramMap.get('id');
    if (courseId) {
      this.loadCourse(courseId);
    }
  }

  loadCourse(courseId: string): void {
    this.courseService.getCourseById(courseId, true).subscribe(
      (course: Course) => {
        this.course = course;

        if (course.userProgress) {
          this.userCourseProgress = course.userProgress;

          const lastLesson: Lesson | null = this.getLessonById(
            course.userProgress.currentLesson
          );

          if (lastLesson) {
            this.selectLesson(lastLesson);
          }
        } else {
          const firstLesson: Lesson | null = this.getFirstLesson(course);
          if (firstLesson) {
            this.selectLesson(firstLesson);
          }
        }
      },
      (error: any) => {
        console.error('Error loading course:', error);
      }
    );
  }

  getFirstLesson(course: Course): Lesson | null {
    for (const module of course.modules) {
      if (module.lessons.length > 0) {
        return module.lessons[0];
      }
    }
    return null;
  }

  getLessonById(lessonId: string): Lesson | null {
    for (const module of this.course.modules) {
      for (const lesson of module.lessons) {
        if (lesson._id === lessonId) {
          return lesson;
        }
      }
    }
    return null;
  }

  completeLesson(lessonId: any, position: number): void {
    this.courseService
      .completeLesson(this.course._id, lessonId, position)
      .subscribe(
        (userCourseProgress: UserCourseProgress) => {
          this.userCourseProgress = userCourseProgress;
        },
        (error: any) => {
          console.error('Error completing lesson:', error);
        }
      );
  }

  isLessonCompleted(lessonId: string): boolean {
    return this.userCourseProgress?.completedLessons.includes(lessonId);
  }

  selectLesson(lesson: Lesson): void {
    this.selectedLesson = lesson;
  }

  toggleModule(index: number): void {
    this.activeModuleIndex = this.activeModuleIndex === index ? null : index;
  }

  selectTab(tab: string) {
    this.selectedTab = tab;
  }
}
