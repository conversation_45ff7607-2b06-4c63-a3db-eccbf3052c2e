<div class="p-8">
  <!-- Search Bar -->
  <div class="mb-8 flex justify-center">
    <div class="relative w-full max-w-xl">
      <div
        class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"
      >
        <span class="material-symbols-outlined text-gray-400 font-light">
          search
        </span>
      </div>
      <input
        type="text"
        name="search"
        id="search"
        class="block w-full rounded-md border-0 py-1.5 pl-10 pr-20 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600"
        placeholder="Search for courses..."
      />
    </div>
  </div>

  <!-- Filters and Sorting -->
  <div class="flex justify-between items-center mb-8">
    <div class="flex space-x-4">
      <button class="px-4 py-2 bg-gray-200 rounded-lg text-sm">Popular</button>
      <button class="px-4 py-2 bg-gray-200 rounded-lg text-sm">Discover</button>
      <button class="px-4 py-2 bg-gray-200 rounded-lg text-sm">
        JavaScript
      </button>
      <button class="px-4 py-2 bg-gray-200 rounded-lg text-sm">
        Web Development
      </button>
      <!-- Add more filter buttons as needed -->
    </div>
    <div>
      <button class="px-4 py-2 bg-gray-200 rounded-lg text-sm">Filters</button>
    </div>
  </div>

  <!-- Courses Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
    <!-- <div
      *ngFor="let course of courses"
      class="bg-white p-4 rounded-lg shadow-md"
    >
      <img
        [src]="course.coverImage"
        alt="Course Image"
        class="w-full h-40 object-cover rounded-lg mb-4"
      />
      <div class="flex">
        <div class="flex-1">
          <p>{{ course.rating }}</p>
        </div>
        <div class="flex">
          {{ course.enrolments }}
        </div>
      </div>
      <h4
        class="mb-2 text-sm font-bold text-gray-600 line-clamp-2 tracking-tight"
        (click)="openCourse(course._id)"
      >
        {{ course.title }}
      </h4>

      <p class="text-gray-600 text-sm line-clamp-2">
        {{ course.description }}
      </p>
    </div> -->
    <!-- Course Card Component -->
    <div class="bg-white rounded-lg shadow-md relative">
      <!-- Badges -->
      <div class="absolute top-2 left-2 space-x-2">
        <span class="bg-yellow-400 text-white text-xs px-2 py-1 rounded"
          >JavaScript</span
        >
        <span class="bg-red-500 text-white text-xs px-2 py-1 rounded"
          >Beginner</span
        >
      </div>

      <!-- Course Image -->
      <img
        src="https://i.ytimg.com/vi/UCwkwDASDKg/maxresdefault.jpg"
        alt="Course Image"
        class="w-full rounded-lg object-cover mb-2"
      />

      <div class="px-4 pb-4">
        <!-- Course Dates -->
        <div class="text-gray-500 text-xs mb-2">
          <span>Created 12 nov 2018</span> &bull;
          <span>435 enrolments</span>
        </div>

        <!-- Course Title -->
        <h4 class="text-lg font-semibold mb-2">Node Hands On</h4>

        <!-- Course Progress -->
        <div class="text-gray-600 text-sm mb-4 line-clamp-2">
          <p>
            Master Node.js with this hands on practical course that will take
            you from zero to master in 5 hours.
          </p>
        </div>
        <hr />

        <!-- Course Links -->
        <div class="text-blue-500 text-sm mb-2">
          <a href="#" class="block mb-1">Course Information</a>
          <a href="#" class="block">Bookmark this course</a>
        </div>

        <hr />

        <!-- Continue Button -->
        <div class="flex justify-center mt-5">
          <button class="bg-blue-500 text-white px-4 py-2 rounded-lg">
            Enrol
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
