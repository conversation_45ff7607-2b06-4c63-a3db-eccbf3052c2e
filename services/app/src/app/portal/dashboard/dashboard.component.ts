import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterLink, RouterLinkActive, RouterOutlet } from '@angular/router';
import { UserService } from '../../core/services/user.service';
import { Subscription } from 'rxjs';
import { EmptyStateComponent } from '../../shared/components/empty-state/empty-state.component';
import { PointsPanelComponent } from './points-panel/points-panel.component';
import { StreaksPanelComponent } from './streaks-panel/streaks-panel.component';
import { User } from '../../core/models/user.model';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    RouterLink,
    RouterLinkActive,
    RouterOutlet,
    CommonModule,
    EmptyStateComponent,
    PointsPanelComponent,
    StreaksPanelComponent,
    FormsModule,
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css',
})
export class DashboardComponent {
  user!: User;
  public totalPoints: number = 0;
  public pointsHistory: any[] = [];
  private userSubscription: Subscription = new Subscription();

  constructor(private userService: UserService) {}

  ngOnInit(): void {
    this.userSubscription = this.userService.getUser().subscribe({
      next: (user) => {
        if (user) {
          this.user = user;
        }
      },
      error: (err) => console.error('Error fetching user profile:', err),
    });
  }

  ngOnDestroy(): void {
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }
}
