<h2 class="font-semibold text-gray-700">Points Overview</h2>
<div class="flex justify-between py-4">
  <div class="bg-white p-6 text-left border flex-1">
    <h2 class="text-sm uppercase font-medium text-gray-400">Today</h2>
    <p class="text-3xl font-bold text-gray-900">
      {{ pointsToday }}
      <span class="text-sm text-gray-500 font-normal ml-1"
        >from {{ pointsYesterday }}</span
      >
    </p>
    <div class="flex mt-2 space-x-1 justify-start">
      <!-- Conditional rendering for up arrow -->
      <svg
        *ngIf="todayPercentageChange > 0"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        class="icon-arrow-thick-up-circle h-5"
      >
        <circle cx="12" cy="12" r="10" class="icon-primary-green" />
        <path
          class="icon-secondary-green"
          d="M13 9.41V17a1 1 0 0 1-2 0V9.41l-2.3 2.3a1 1 0 1 1-1.4-1.42l4-4a1 1 0 0 1 1.4 0l4 4a1 1 0 0 1-1.4 1.42L13 9.4z"
        />
      </svg>

      <!-- Conditional rendering for down arrow -->
      <svg
        *ngIf="todayPercentageChange < 0"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        class="icon-arrow-thin-down-circle h-5"
      >
        <circle cx="12" cy="12" r="10" class="icon-primary-red" />
        <path
          class="icon-secondary-red"
          d="M11 14.59V7a1 1 0 0 1 2 0v7.59l2.3-2.3a1 1 0 1 1 1.4 1.42l-4 4a1 1 0 0 1-1.4 0l-4-4a1 1 0 0 1 1.4-1.42l2.3 2.3z"
        />
      </svg>

      <!-- Display the value with conditional text color -->
      <p
        [ngClass]="{
          'text-green-600': todayPercentageChange > 0,
          'text-red-600': todayPercentageChange < 0,
          'text-gray-500': todayPercentageChange === 0
        }"
        class="font-semibold text-sm"
      >
        {{ todayPercentageChange | number : "1.1-2" }}%
      </p>
    </div>
  </div>
  <div class="bg-white p-6 text-left border-t border-b flex-1">
    <h2 class="text-sm uppercase font-medium text-gray-400">This Week</h2>
    <p class="text-3xl font-bold text-gray-900">
      {{ pointsThisWeek }}
      <span class="text-sm text-gray-500 font-normal ml-1"
        >from {{ pointsLastWeek }}</span
      >
    </p>
    <div class="flex mt-2 space-x-2 justify-start">
      <!-- Conditional rendering for up arrow -->
      <svg
        *ngIf="weekPercentageChange > 0"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        class="icon-arrow-thick-up-circle h-5"
      >
        <circle cx="12" cy="12" r="10" class="icon-primary-green" />
        <path
          class="icon-secondary-green"
          d="M13 9.41V17a1 1 0 0 1-2 0V9.41l-2.3 2.3a1 1 0 1 1-1.4-1.42l4-4a1 1 0 0 1 1.4 0l4 4a1 1 0 0 1-1.4 1.42L13 9.4z"
        />
      </svg>

      <!-- Conditional rendering for down arrow -->
      <svg
        *ngIf="weekPercentageChange < 0"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        class="icon-arrow-thin-down-circle h-5"
      >
        <circle cx="12" cy="12" r="10" class="icon-primary-red" />
        <path
          class="icon-secondary-red"
          d="M11 14.59V7a1 1 0 0 1 2 0v7.59l2.3-2.3a1 1 0 1 1 1.4 1.42l-4 4a1 1 0 0 1-1.4 0l-4-4a1 1 0 0 1 1.4-1.42l2.3 2.3z"
        />
      </svg>

      <!-- Display the value with conditional text color -->
      <p
        [ngClass]="{
          'text-green-600': weekPercentageChange > 0,
          'text-red-600': weekPercentageChange < 0,
          'text-gray-500': weekPercentageChange === 0
        }"
        class="font-semibold text-sm"
      >
        {{ weekPercentageChange | number : "1.1-2" }}%
      </p>
    </div>
  </div>
  <div class="bg-white p-6 text-left border flex-1">
    <h2 class="text-sm uppercase font-medium text-gray-400">Total Points</h2>
    <p class="text-3xl font-bold text-gray-900">
      {{ totalPoints }}
      <span class="text-sm text-gray-500 font-normal ml-1"></span>
    </p>
    <span
      class="inline-flex items-center rounded-md bg-blue-50 px-2 mt-3 text-sm font-bold"
      [ngClass]="getLevelClass()"
      >Level {{ level }}</span
    >
  </div>
</div>
