import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface PointsEvent {
  event: string;
  points: number;
  created: number;
  _id: string;
  userId: string;
}

export interface Points {
  total: number;
  history: PointsEvent[];
}

@Component({
  selector: 'app-points-panel',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './points-panel.component.html',
  styleUrl: './points-panel.component.css',
})
export class PointsPanelComponent {
  @Input() points!: Points;
  @Input() level: number | null = null;

  totalPoints!: number;
  pointsToday!: number;
  pointsYesterday!: number;
  pointsThisWeek!: number;
  pointsLastWeek!: number;
  todayPercentageChange!: number;
  weekPercentageChange!: number;

  ngOnInit(): void {
    const now = Date.now();
    const startOfToday = new Date(new Date().setHours(0, 0, 0, 0)).getTime();
    const startOfYesterday = startOfToday - 86400000;
    const startOfWeek = new Date(now - new Date().getDay() * 86400000).setHours(
      0,
      0,
      0,
      0
    );
    const startOfLastWeek = startOfWeek - 604800000;

    this.totalPoints = this.calculateTotalPoints();
    this.pointsToday = this.calculatePointsForPeriod(startOfToday);
    this.pointsYesterday = this.calculatePointsForPeriod(
      startOfYesterday,
      startOfToday
    );
    this.pointsThisWeek = this.calculatePointsForPeriod(startOfWeek);
    this.pointsLastWeek = this.calculatePointsForPeriod(
      startOfLastWeek,
      startOfWeek
    );

    this.todayPercentageChange = this.calculatePercentageChange(
      this.pointsToday,
      this.pointsYesterday
    );
    this.weekPercentageChange = this.calculatePercentageChange(
      this.pointsThisWeek,
      this.pointsLastWeek
    );
  }

  calculatePointsForPeriod(startDate: number, endDate?: number): number {
    return this.points.history
      .filter(
        (event) =>
          event.created >= startDate &&
          (endDate ? event.created < endDate : true)
      )
      .reduce((total, event) => total + event.points, 0);
  }

  calculateTotalPoints(): number {
    return this.points.total;
  }

  calculatePercentageChange(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  }

  getLevelClass(): string {
    switch (this.level) {
      default:
        return 'bg-blue-50 text-blue-700';
    }
  }
}
