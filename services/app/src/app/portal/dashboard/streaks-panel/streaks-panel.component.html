<h2 class="font-semibold text-gray-700">Streaks Overview</h2>
<div class="flex justify-between py-4">
  <!-- Momentum Streak -->
  <div class="bg-white p-6 text-left border flex-1">
    <h2 class="text-sm uppercase font-medium text-gray-400">DAILY LOGIN</h2>
    <p class="text-3xl font-bold text-gray-900">
      {{ streaks.daily.count }}
      <span class="text-base font-normal text-gray-600">{{
        streaks.daily.count <= 7 ? "day streak" : "week streak"
      }}</span>
    </p>
    <div class="flex mt-2 space-x-2 justify-start">
      <ng-container *ngIf="streaks.daily.count <= 7">
        <ng-container *ngFor="let day of getDailyStreakDays(); let i = index">
          <div
            class="w-4 h-4 rounded-full"
            [ngClass]="day ? 'bg-emerald-400' : 'bg-gray-300'"
            attr.aria-label="Streak day {{ i + 1 }}"
          ></div>
        </ng-container>
      </ng-container>
      <ng-container *ngIf="streaks.daily.count > 7">
        <ng-container
          *ngFor="
            let week of getWeeklyStreakWeeks(streaks.daily.count);
            let i = index
          "
        >
          <div class="flex space-x-1">
            <ng-container *ngFor="let day of week; let j = index">
              <div
                class="w-4 h-4 rounded-full"
                [ngClass]="day ? 'bg-emerald-400' : 'bg-gray-300'"
                attr.aria-label="Week {{ i + 1 }} day {{ j + 1 }}"
              ></div>
            </ng-container>
          </div>
        </ng-container>
      </ng-container>
    </div>
  </div>

  <!-- Course Streaks -->
  <div class="bg-white p-6 text-left border-t border-b flex-1">
    <h2 class="text-sm uppercase font-medium text-gray-400">COURSE PROGRESS</h2>
    <p class="text-3xl font-bold text-gray-900">
      {{ streaks.activity.courseProgress.count }}
      <span class="text-base font-normal text-gray-600">{{
        streaks.activity.courseProgress.count <= 7
          ? "day streak"
          : "week streak"
      }}</span>
    </p>
    <div class="flex mt-2 space-x-1 justify-start">
      <ng-container *ngIf="streaks.activity.courseProgress.count <= 7">
        <ng-container *ngFor="let day of getDailyStreakDays(); let i = index">
          <div
            class="w-4 h-4 rounded-full"
            [ngClass]="day ? 'bg-emerald-400' : 'bg-gray-300'"
            attr.aria-label="Streak day {{ i + 1 }}"
          ></div>
        </ng-container>
      </ng-container>
      <ng-container *ngIf="streaks.activity.courseProgress.count > 7">
        <ng-container
          *ngFor="
            let week of getWeeklyStreakWeeks(
              streaks.activity.courseProgress.count
            );
            let i = index
          "
        >
          <div class="flex space-x-1">
            <ng-container *ngFor="let day of week; let j = index">
              <div
                class="w-4 h-4 rounded-full"
                [ngClass]="day ? 'bg-emerald-400' : 'bg-gray-300'"
                attr.aria-label="Week {{ i + 1 }} day {{ j + 1 }}"
              ></div>
            </ng-container>
          </div>
        </ng-container>
      </ng-container>
    </div>
  </div>

  <!-- Study Streak -->
  <div class="bg-white p-6 text-left border flex-1">
    <h2 class="text-sm uppercase font-medium text-gray-400">STUDY SESSIONS</h2>
    <p class="text-3xl font-bold text-gray-900">
      {{ streaks.activity.studySession.count }}
      <span class="text-base font-normal text-gray-600">{{
        streaks.activity.studySession.count <= 7 ? "day streak" : "week streak"
      }}</span>
    </p>
    <div class="flex mt-2 space-x-1 justify-start">
      <ng-container *ngIf="streaks.activity.studySession.count <= 7">
        <ng-container *ngFor="let day of getDailyStreakDays(); let i = index">
          <div
            class="w-4 h-4 rounded-full"
            [ngClass]="day ? 'bg-emerald-400' : 'bg-gray-300'"
            attr.aria-label="Streak day {{ i + 1 }}"
          ></div>
        </ng-container>
      </ng-container>
      <ng-container *ngIf="streaks.activity.studySession.count > 7">
        <ng-container
          *ngFor="
            let week of getWeeklyStreakWeeks(
              streaks.activity.studySession.count
            );
            let i = index
          "
        >
          <div class="flex space-x-1">
            <ng-container *ngFor="let day of week; let j = index">
              <div
                class="w-4 h-4 rounded-full"
                [ngClass]="day ? 'bg-emerald-400' : 'bg-gray-300'"
                attr.aria-label="Week {{ i + 1 }} day {{ j + 1 }}"
              ></div>
            </ng-container>
          </div>
        </ng-container>
      </ng-container>
    </div>
  </div>
</div>
