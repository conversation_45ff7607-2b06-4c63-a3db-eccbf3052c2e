import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Streaks } from '../../../core/models/user.model';

@Component({
  selector: 'app-streaks-panel',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './streaks-panel.component.html',
  styleUrl: './streaks-panel.component.css',
})
export class StreaksPanelComponent {
  @Input() streaks!: Streaks;

  getDailyStreakDays() {
    const days = new Array(7).fill(false);
    const count = Math.min(this.streaks.daily.count, 7);
    for (let i = 0; i < count; i++) {
      days[i] = true;
    }
    return days; // Most recent day should be on the right
  }

  getWeeklyStreakWeeks(count: any) {
    const weeks = [];
    const fullWeeks = Math.floor(count / 7);
    const remainingDays = count % 7;

    for (let i = 0; i < fullWeeks; i++) {
      weeks.push(new Array(7).fill(true));
    }

    if (remainingDays > 0) {
      const lastWeek = new Array(7).fill(false);
      for (let i = 0; i < remainingDays; i++) {
        lastWeek[i] = true;
      }
      weeks.push(lastWeek);
    }

    return weeks;
  }
}
