<div class="container mx-auto p-4">
  <h1 class="text-2xl font-bold mb-4">Notes</h1>
  <p class="text-md font-light text-gray-600 mb-6 w-3/5">
    Notes allow you to crystallize your knowledge as you learn. As you process
    and go through your artifacts, you can quickly take notes, and they will be
    stored in this section.
  </p>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    <div
      *ngFor="let note of notes"
      class="bg-white shadow-lg rounded-lg overflow-hidden p-4"
    >
      <h2 class="text-lg font-semibold mb-2">{{ note.title }}</h2>
      <hr class="border-t-1 border-gray-200 dark:border-gray-700" />
      <div class="prose">
        <markdown>{{ note.content }}</markdown>
      </div>
      <hr class="border-t-1 border-gray-200 dark:border-gray-700" />
      <div class="mt-4 text-sm text-gray-500">
        <p>Created by: {{ note.createdBy }}</p>
        <p>Created on: {{ note.created | date : "short" }}</p>
        <p>Updated on: {{ note.updated | date : "short" }}</p>
      </div>
    </div>
  </div>
</div>
