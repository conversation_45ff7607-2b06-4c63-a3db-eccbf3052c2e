import { Component } from '@angular/core';
import { NoteService } from '../../core/services/note.service';
import { Note } from '../../core/models/note.model';
import { CommonModule } from '@angular/common';

import { MarkdownModule } from 'ngx-markdown';

@Component({
  selector: 'app-notes',
  standalone: true,
  imports: [CommonModule, MarkdownModule],
  templateUrl: './notes.component.html',
  styleUrl: './notes.component.css',
})
export class NotesComponent {
  notes: Note[] = [];
  constructor(private noteService: NoteService) {}

  ngOnInit(): void {
    this.fetchNotesByCreator();
  }

  fetchNotesByCreator(): void {
    this.noteService.fecthUserNotes().subscribe(
      (data: Note[]) => {
        this.notes = data;
      },
      (error) => {
        console.error('Error fetching notes:', error);
      }
    );
  }
}
