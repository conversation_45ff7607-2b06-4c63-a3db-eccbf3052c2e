import { Component } from '@angular/core';
import { YouTubePlayer } from '@angular/youtube-player';
import { Router, ActivatedRoute } from '@angular/router';
import { StudySessionService } from '../../core/services/study-session.service';

@Component({
  selector: 'app-player',
  standalone: true,
  imports: [YouTubePlayer],
  templateUrl: './player.component.html',
  styleUrl: './player.component.css',
})
export class PlayerComponent {
  artifact: any;
  isYoutubeVideo: boolean = false;
  videoId: string = '';
  height: number = 390;
  width: number = 640;
  startSeconds: number = 0;
  endSeconds: number | undefined;
  suggestedQuality: any = 'hd720';
  playerVars = {
    autoplay: 1,
    controls: 1,
    loop: 0,
    rel: 0,
  };
  showBeforeIframeApiLoads: boolean = true;

  constructor(
    private router: Router,
    private studySessionService: StudySessionService
  ) {}

  ngOnInit(): void {
    this.artifact = this.studySessionService.getArtifact();
    this.isYoutubeVideo = this.studySessionService.isYouTubeVideo();
    this.videoId = this.studySessionService.getYouTubeVideoId();
  }

  onPlayerStateChange(event: any): void {
    debugger;
    console.log('Player state changed:', event.data);
    // Handle state changes (e.g., playing, paused)
  }
}
