.portal {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.navbar {
  padding: 1rem;
  text-align: center;
  font-size: 1.5rem;
  font-weight: bold;
}

.navbar-brand {
  margin: 0;
}

.portal-body {
  display: flex;
  flex: 1;
}

.sidebar {
  background-color: #333;
  color: white;
  width: 200px;
  padding: 1rem;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.sidebar ul {
  list-style-type: none;
  padding: 0;
}

.sidebar ul li {
  margin: 1rem 0;
}

.sidebar ul li a {
  color: white;
  text-decoration: none;
  display: block;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.sidebar ul li a:hover {
  background-color: #444;
}

.main-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.progress-section,
.news-section {
  margin-bottom: 2rem;
}

h1,
h2 {
  color: #333;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  color: white;
}

.navbar-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.navbar-profile {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.profile-picture {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 10px;
}

.profile-info {
  display: flex;
  flex-direction: column;
  margin-right: 20px;
}

.profile-info span {
  font-size: 14px;
}

.logout-button {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 5px 10px;
  cursor: pointer;
  border-radius: 5px;
}

.logout-button:hover {
  background-color: #d32f2f;
}

.portal-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}
