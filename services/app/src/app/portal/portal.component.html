<!-- <div class="flex h-screen">

  <app-side-bar class="sticky top-0 h-screen flex-shrink-0 w-64"></app-side-bar>

  <div class="flex-1 flex flex-col">

    <app-top-nav [user]="user" class="sticky top-0 z-10 w-full"></app-top-nav>


    <div class="flex-1 p-8 overflow-auto">
      <router-outlet></router-outlet>
    </div>
  </div>
</div> -->

<div appCommandPaletteTrigger class="flex h-screen bg-slate-50">
  <!-- Sidebar -->
  <app-side-bar
    class="sticky top-0 h-screen flex-shrink-0 transition-all duration-300"
    (toggle)="isCollapsed = $event"
    [ngClass]="{ 'w-64': !isCollapsed, 'w-20': isCollapsed }"
  ></app-side-bar>

  <!-- Main content area -->
  <div class="flex-1 flex flex-col">
    <!-- Top nav -->
    <app-top-nav></app-top-nav>

    <!-- Main content -->
    <div class="flex-1 p-8 overflow-auto">
      <router-outlet></router-outlet>
    </div>
  </div>
</div>
