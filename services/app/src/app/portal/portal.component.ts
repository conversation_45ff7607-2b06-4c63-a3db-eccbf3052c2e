import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink, RouterLinkActive, RouterOutlet } from '@angular/router';
import { UserService } from '../../app/core/services/user.service';
import { ThemeService } from '../../app/core/services/theme.service';
import { User } from '../core/models/user.model';
import { Subscription } from 'rxjs';
import { WebsocketService } from '../core/services/websocket.service';

import { SideBarComponent } from './../shared/components/side-bar/side-bar.component';
import { TopNavComponent } from './../shared/components/top-nav/top-nav.component';
import { CommandPalettesComponent } from './../shared/components/command-palettes/command-palettes.component';
import { CommandPaletteService } from '../core/services/command-palette.service';
import { CommandPaletteTriggerDirective } from '../core/directives/command-palette-trigger.directive';
@Component({
  selector: 'app-portal',
  standalone: true,
  imports: [
    RouterLink,
    RouterLinkActive,
    RouterOutlet,
    CommonModule,
    SideBarComponent,
    TopNavComponent,
    CommandPalettesComponent,
    CommandPaletteTriggerDirective,
    CommandPalettesComponent,
  ],
  templateUrl: './portal.component.html',
  styleUrl: './portal.component.css',
})
export class PortalComponent {
  user: User | null = null;
  isCollapsed = false;

  private userSubscription!: Subscription;
  private pointsEarnedSubscription: Subscription = new Subscription();

  constructor(
    private userService: UserService,
    private themeService: ThemeService,
    private websocketService: WebsocketService
  ) {}

  ngOnInit(): void {
    this.userSubscription = this.userService.currentUser.subscribe((user) => {
      this.user = user;
    });
    this.pointsEarnedSubscription = this.websocketService
      .onPointsEarned()
      .subscribe({
        next: (message: any) => {
          this.userService.refreshUser();
        },
        error: (err) => {
          console.error('Error receiving points-earned message:', err);
        },
      });
  }

  ngOnDestroy(): void {
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
    if (this.pointsEarnedSubscription) {
      this.pointsEarnedSubscription.unsubscribe();
    }
    if (this.websocketService) {
      this.websocketService.disconnect();
    }
  }

  logout(): void {
    console.log('Logging out...');
    // this.userService.logout();
    this.user = null;
  }

  toggleTheme() {
    this.themeService.toggleTheme();
  }

  onSidebarToggle(collapsed: boolean) {
    this.isCollapsed = collapsed;
  }
}
