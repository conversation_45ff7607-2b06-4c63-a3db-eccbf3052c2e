.card {
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 16px;
  max-width: 400px;
  margin: 0 auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.card-header {
  margin-bottom: 16px;
}

.card-body {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.btn {
  background-color: #007bff;
  border: none;
  color: white;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 8px 0;
  cursor: pointer;
  border-radius: 8px;
  width: 100%;
}

.btn-google {
  background-color: #db4437;
}

.btn-github {
  background-color: #333;
}

.btn a {
  color: white;
  text-decoration: none;
}

.subtext {
  margin-top: 16px;
  font-size: 0.9em;
}

.subtext a {
  color: #007bff;
  text-decoration: none;
}
