<!-- src/app/sign-in/sign-in.component.html -->
<div class="min-h-screen flex items-center justify-center bg-gray-50">
  <div class="max-w-md w-full bg-white shadow-md rounded-lg p-8 space-y-6">
    <div class="flex justify-center mb-4">
      <img src="assets/icons/ds-word-mark.png" alt="Logo" class="w-20" />
    </div>
    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
      Sign in to your account
    </h2>
    <div class="mt-4 space-y-3">
      <button
        (click)="signInWith('google')"
        class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
      >
        <img
          src="assets/icons/google-icon.png"
          alt="Google"
          class="w-5 h-5 mr-2"
        />
        Sign in with Google
      </button>
      <button
        class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
      >
        <img
          src="/assets/icons/github-mark.png"
          alt="Apple"
          class="w-5 h-5 mr-2"
        />
        Sign in with GitHub
      </button>
    </div>
    <div class="relative my-6">
      <div class="absolute inset-0 flex items-center">
        <div class="w-full border-t border-gray-300"></div>
      </div>
      <div class="relative flex justify-center text-sm">
        <span class="px-2 bg-white text-gray-500">or use Email</span>
      </div>
    </div>
    <form class="space-y-6">
      <div class="rounded-md shadow-sm -space-y-px">
        <div>
          <label for="email-address" class="sr-only">Email address</label>
          <input
            id="email-address"
            name="email"
            type="email"
            autocomplete="email"
            required
            class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
            placeholder="Email address"
          />
        </div>
        <div>
          <label for="password" class="sr-only">Password</label>
          <input
            id="password"
            name="password"
            type="password"
            autocomplete="current-password"
            required
            class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
            placeholder="Password"
          />
        </div>
      </div>
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <input
            id="remember-me"
            name="remember-me"
            type="checkbox"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
          <label for="remember-me" class="ml-2 block text-sm text-gray-900"
            >Remember me on this device</label
          >
        </div>
        <div class="text-sm">
          <a href="#" class="font-medium text-slate-600 hover:text-indigo-500"
            >Forgot password?</a
          >
        </div>
      </div>
      <div>
        <button
          type="submit"
          class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-900 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Sign In
        </button>
      </div>
      <div class="text-center text-sm text-gray-600">
        Not a member?
        <a href="#" class="font-medium text-slate-600 hover:text-indigo-500"
          >Start a 14-day free trial!</a
        >
      </div>
    </form>
  </div>
</div>

<!-- <div class="card">
  <div class="card-header">
    <h2>Welcome!</h2>
    <p>Please sign in to continue</p>
  </div>
  <div class="card-body">
    <button class="btn btn-google">
      <a href="http://localhost:3000/api/auth/google">Sign In with Google</a>
    </button>
    <button class="btn btn-github">
      <a href="http://localhost:3000/api/auth/github">Sign In with GitHub</a>
    </button>
    <p class="subtext">
      If you don't have an account with either, you can
      <a href="/register">register here</a>.
    </p>
  </div>
</div> -->
