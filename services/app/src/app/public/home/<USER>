import { Component, OnInit } from '@angular/core';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { ApiService } from '../../core/services/api.service';
import { Ping } from '../../core/models/ping.model';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [RouterLink, RouterLinkActive],
  templateUrl: './home.component.html',
  styleUrl: './home.component.css',
})
export class HomeComponent {
  pingResult: Ping = {} as Ping;

  constructor(private apiService: ApiService) {}

  ngOnInit(): void {}

  pingServer(): void {
    this.apiService.ping().subscribe((response) => {
      console.log(JSON.stringify(response, null, 2));
      this.pingResult = response.message;
    }),
      () => {};
  }
}
