.card {
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 16px;
  max-width: 500px;
  margin: 0 auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  text-align: center;
  margin-bottom: 16px;
}

.card-body {
  text-align: center;
}

.profile-info {
  margin-bottom: 16px;
}

.profile-picture {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 8px;
}

.form-group {
  margin-bottom: 16px;
  text-align: left;
}

.form-group input[type="checkbox"] {
  margin-right: 8px;
}

.btn {
  background-color: #007bff;
  border: none;
  color: white;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
  border-radius: 8px;
}

.btn-primary {
  background-color: #007bff;
}
