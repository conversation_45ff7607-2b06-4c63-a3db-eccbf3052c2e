<div class="card">
  <div class="card-header">
    <h2>Create Your Account</h2>
  </div>
  <div class="card-body">
    <div class="profile-info">
      <img [src]="picture" alt="Profile Picture" class="profile-picture" />
      <p>{{ firstName }}</p>
      <p>{{ email }}</p>
    </div>
    <form (ngSubmit)="onSubmit()">
      <div class="form-group">
        <input
          type="checkbox"
          id="terms"
          [(ngModel)]="agreedToTerms"
          name="terms"
          required
        />
        <label for="terms"
          >I agree to the <a href="/terms">Terms of Service</a></label
        >
      </div>
      <div class="form-group">
        <input
          type="checkbox"
          id="privacy"
          [(ngModel)]="agreedToPrivacy"
          name="privacy"
          required
        />
        <label for="privacy"
          >I agree to the <a href="/privacy">Privacy Policy</a></label
        >
      </div>
      <div class="form-group">
        <input
          type="checkbox"
          id="marketing"
          [(ngModel)]="optInForMarketing"
          name="marketing"
        />
        <label for="marketing">I agree to receive marketing emails</label>
      </div>
      <button type="submit" class="btn btn-primary">Get Started</button>
    </form>
  </div>
</div>
