import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { ApiService } from '../../../core/services/api.service';
import { UserService } from '../../../core/services/user.service';

@Component({
  selector: 'app-create-account',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './create-account.component.html',
  styleUrl: './create-account.component.css',
})
export class CreateAccountComponent {
  id: string | undefined;
  firstName?: string;
  email?: string;
  picture?: string;
  language?: string;
  agreedToTerms: boolean = false;
  agreedToPrivacy: boolean = false;
  optInForMarketing: boolean = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private apiService: ApiService,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.id = params['id'];
      this.firstName = params['name'];
      this.email = params['email'];
      this.picture = params['picture'];
      this.language = params['language'];
    });
  }

  onSubmit(): void {
    if (this.agreedToTerms && this.agreedToPrivacy) {
      // Handle form submission logic, e.g., send data to backend
      // Redirect to another page after successful registration

      if (this.id) {
        this.userService
          .updateUser(this.id, {
            marketing: {
              email: this.optInForMarketing,
            },
            policies: {
              terms: `0011`,
              privacy: `0001`,
            },
            activated: true,
          })
          .subscribe((user) => {
            console.log(JSON.stringify(user, null, 2));
            this.router.navigate(['/dashboard']);
          });
      }

      //this.router.navigate(['/dashboard']);
    } else {
      alert('Please agree to the terms and privacy policy.');
    }
  }
}
