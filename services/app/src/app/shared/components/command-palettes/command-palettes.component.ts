import { ApplicationRef, Component, ComponentRef } from '@angular/core';

@Component({
  selector: 'app-command-palettes',
  standalone: true,
  imports: [],
  templateUrl: './command-palettes.component.html',
  styleUrl: './command-palettes.component.css',
})
export class CommandPalettesComponent {
  componentRef!: ComponentRef<any>;

  constructor(private appRef: ApplicationRef) {}

  exit() {
    this.appRef.detachView(this.componentRef.hostView);
    this.componentRef.destroy();
  }
}
