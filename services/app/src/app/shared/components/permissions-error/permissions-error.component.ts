import { Component, Input, ApplicationRef, ComponentRef } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-permissions-error',
  standalone: true,
  imports: [],
  templateUrl: './permissions-error.component.html',
  styleUrl: './permissions-error.component.css',
})
export class PermissionsErrorComponent {
  @Input() message: string | undefined;
  componentRef!: ComponentRef<any>;

  constructor(private appRef: ApplicationRef, private router: Router) {}

  upgradeAccount(): void {
    console.log('upgrade account');
  }

  exit() {
    this.appRef.detachView(this.componentRef.hostView);
    this.componentRef.destroy();
    this.router.navigate(['/portal/dashboard']);
  }
}
