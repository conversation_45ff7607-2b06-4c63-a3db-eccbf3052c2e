<div
  class="flex justify-between items-center p-4 bg-white dark:bg-gray-900 shadow"
>
  <div>
    <h1 class="text-xl font-bold text-gray-900 dark:text-gray-100">
      {{ course.title }}
    </h1>
    <p>Course Progress: {{ getProgress() }}%</p>
  </div>
  <div class="flex items-center space-x-4">
    <div class="relative">
      <ul>
        <li>
          <a [routerLink]="['/portal']">
            <span class="material-symbols-outlined"> home </span> Dashboard
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>
