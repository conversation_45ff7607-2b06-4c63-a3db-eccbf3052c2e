import { Component, Input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { Course } from '../../../core/models/course.model';

@Component({
  selector: 'app-player-top-nav',
  standalone: true,
  imports: [RouterLink],
  templateUrl: './player-top-nav.component.html',
  styleUrl: './player-top-nav.component.css',
})
export class PlayerTopNavComponent {
  @Input() course!: Course;
  @Input() progress: number = 0;

  getProgress() {
    return Math.round(this.progress);
  }
}
