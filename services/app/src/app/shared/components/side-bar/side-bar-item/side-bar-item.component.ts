import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MenuItem } from '../side-bar.component';

@Component({
  selector: 'app-side-bar-item',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './side-bar-item.component.html',
  styleUrl: './side-bar-item.component.css',
})
export class SideBarItemComponent {
  @Input() item!: MenuItem;
  @Input() isCollapsed!: boolean;
}
