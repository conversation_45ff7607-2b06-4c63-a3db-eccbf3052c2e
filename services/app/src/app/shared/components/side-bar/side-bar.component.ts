import { Component, EventEmitter, Output } from '@angular/core';
import { RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { SideBarItemComponent } from './side-bar-item/side-bar-item.component';

export interface MenuItem {
  routerLink: string;
  icon: string;
  label: string;
}

@Component({
  selector: 'app-side-bar',
  standalone: true,
  imports: [RouterLink, CommonModule, SideBarItemComponent],
  templateUrl: './side-bar.component.html',
  styleUrl: './side-bar.component.css',
})
export class SideBarComponent {
  @Output() toggle = new EventEmitter<boolean>();
  isCollapsed: boolean = false;

  mainMenu: MenuItem[] = [
    { routerLink: '/portal/dashboard', icon: 'route', label: 'Pathfinder' },
    { routerLink: '/portal/dashboard', icon: 'query_stats', label: 'Discover' },
  ];

  secondaryMenu: MenuItem[] = [
    { routerLink: '/portal/dashboard', icon: 'dashboard', label: 'Dashboard' },
    { routerLink: '/portal/artifacts', icon: 'two_pager', label: 'Artifacts' },
    { routerLink: '/portal/notes', icon: 'summarize', label: 'Notes' },
    {
      routerLink: '/portal/courses',
      icon: 'cast_for_education',
      label: 'Courses',
    },
    { routerLink: '/portal/dashboard', icon: 'forum', label: 'Community' },
  ];

  settingsMenu: MenuItem[] = [
    { routerLink: '/portal/dashboard', icon: 'settings', label: 'Settings' },
    { routerLink: '/portal/profile', icon: 'account_circle', label: 'Profile' },
  ];

  toggleCollapse() {
    this.isCollapsed = !this.isCollapsed;
    this.toggle.emit(this.isCollapsed);
  }
}
