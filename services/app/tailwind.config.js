/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: "class",
  content: ["./src/**/*.{html,ts}"],
  theme: {
    extend: {
      colors: {
        // Custom colors for light theme
        primary: {
          light: "#f2f3f1", // Example primary color for light mode
          dark: "#2c2c2c", // Example primary color for dark mode
        },
        background: {
          light: "#f2f3f1", // Light background color
          dark: "#2c2c2c", // Dark background color
        },
        text: {
          light: "#2c2c2c", // Light text color
          dark: "#f2f3f1", // Dark text color
        },
        // Add more custom colors as needed
      },
    },
  },
  plugins: [require("@tailwindcss/typography"), require("@tailwindcss/forms")],
};
