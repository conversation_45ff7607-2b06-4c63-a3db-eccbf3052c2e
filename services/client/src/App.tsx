import React from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import Header from './components/Header';
import LearningCenter from './pages/LearningCenter';
import ArtifactVault from './pages/ArtifactVault';
import Dashboard from './pages/Dashboard';
import CourseBuilder from './pages/CourseBuilder';
import Courses from './pages/Courses';
import Login from './pages/Login';
import Signup from './pages/Signup';
import RequireAuth from './components/RequireAuth';
import Logout from './pages/Logout';
import NotFound from './pages/NotFound';
import Profile from './pages/Profile';

import RequireRole from './components/RequiredRole';
import { AccessControlProvider } from './context/AccessControlContext';
const App: React.FC = () => {
  const location = useLocation();
  const isLoginPage = location.pathname === '/login';

  // Define valid routes with pattern matching
  const validRoutes = [
    '/login',
    '/logout',
    '/',
    '/courses',
    '/vault',
    '/builder',
    '/profile',
    '/learning',
  ];

  // Check if the current path matches any valid route pattern
  const isNotFoundPage =
    !validRoutes.includes(location.pathname) &&
    !location.pathname.match(/^\/learning\/[^/]+$/); // Matches /learning/{courseId}

  return (
    <AuthProvider>
      <AccessControlProvider>
      <div className="min-h-full">
        {!isLoginPage && !isNotFoundPage && <Header />}
        <main>
          <Routes>
            {/* Public Route */}
            <Route path="/login" element={<Login />} />
            <Route path="/logout" element={<Logout />} />
            <Route path="/create-account" element={<Signup />} />
            {/* Protected Routes */}
            <Route
              path="/"
              element={
                <RequireAuth>
                  <Dashboard />
                </RequireAuth>
              }
            />
            <Route
              path="/courses"
              element={
                <RequireAuth>
                  <RequireRole requiredRole="basic" feature='Courses'>
                   <Courses />
                   </RequireRole>
                </RequireAuth>
              }
            />
            <Route
              path="/vault"
              element={
                <RequireAuth>
                  <RequireRole requiredRole="basic" feature='Courses'>
                  <ArtifactVault />
                  </RequireRole>
                </RequireAuth>
              }
            />
            <Route
              path="/builder"
              element={
                <RequireAuth>
                  <CourseBuilder />
                </RequireAuth>
              }
            />
            <Route
              path="/learning/:courseId?"
              element={
                <RequireAuth>
                  <LearningCenter />
                </RequireAuth>
              }
            />
            <Route
              path="/profile"
              element={
                <RequireAuth>
                  <Profile />
                </RequireAuth>
              }
            />
            {/* Catch-all 404 route - must be last */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </main>
      </div>
    </AccessControlProvider>
    </AuthProvider>
  );
};

export default App;
