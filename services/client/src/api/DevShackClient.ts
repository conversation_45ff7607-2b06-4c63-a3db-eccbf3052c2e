import { AxiosRequestConfig, AxiosResponse } from 'axios';

import httpClient from './HttpClient';
import { ServicePrefix, ServiceConfig } from '../types/ServicePrefix';

type ParamsType = Record<string, unknown>;

/**
 * Wrapper to interact with the BriefAI API.
 */
const DevShackClient = {
  get: async <T>(
    resource: string,
    params?: ParamsType,
    servicePrefix: ServicePrefix = ServicePrefix.API
  ): Promise<T> => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: resource,
      params,
      baseURL: ServiceConfig[servicePrefix],
    };

    const response: AxiosResponse<T> = await httpClient.request<T>(config);
    return response.data;
  },

  delete: async <T>(
    resource: string,
    servicePrefix: ServicePrefix = ServicePrefix.API
  ): Promise<T> => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: resource,
      baseURL: ServiceConfig[servicePrefix],
    };
    const response: AxiosResponse<T> = await httpClient.request<T>(config);
    return response.data;
  },

  post: async <T>(
    resource: string,
    data: any,
    onUploadProgress?: (progressEvent: ProgressEvent) => void,
    servicePrefix: ServicePrefix = ServicePrefix.API
  ): Promise<T> => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: resource,
      data,
      baseURL: ServiceConfig[servicePrefix],
      onUploadProgress,
    };
    const response: AxiosResponse<T> = await httpClient.request<T>(config);
    return response.data;
  },

  put: async <T>(
    resource: string,
    data: any,
    servicePrefix: ServicePrefix = ServicePrefix.API
  ): Promise<T> => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: resource,
      data,
      baseURL: ServiceConfig[servicePrefix],
    };
    const response: AxiosResponse<T> = await httpClient.request<T>(config);
    return response.data;
  },

  patch: async <T>(
    resource: string,
    data: any,
    servicePrefix: ServicePrefix = ServicePrefix.API
  ): Promise<T> => {
    const config: AxiosRequestConfig = {
      method: 'PATCH',
      url: resource,
      data,
      baseURL: ServiceConfig[servicePrefix],
    };
    const response: AxiosResponse<T> = await httpClient.request<T>(config);
    return response.data;
  },
};

export default DevShackClient;
