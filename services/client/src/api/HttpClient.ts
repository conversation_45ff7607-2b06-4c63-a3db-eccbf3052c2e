import axios, { AxiosError, AxiosResponse } from 'axios';

import {
  NotFoundError,
  PayloadError,
  UnauthorizedError,
  ServerError,
  NotModifiedError,
} from '../lib/error';

/**
 * Wrapper for axios library.
 * User httpClient instance to configure
 * axios http instance.
 */
const httpClient = axios.create({
  timeout: 10000,
  withCredentials: true,
});

/**
 * Request interceptor to add Bear<PERSON> token
 */
// httpClient.interceptors.request.use(
//   (conf) => {
//     // eslint-disable-next-line prefer-const
//     return conf;
//   },
//   (error: AxiosError) => Promise.reject(error)
// );

/**
 * Attach an interceptor to handle errors types.
 * TODO - Implement error handling for
 * different types of errors.
 */
httpClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error: AxiosError) => {
    const { response, request } = error;

    if (response) {
      const { status, data } = response;
      switch (status) {
        case 304: {
          return Promise.reject(new NotModifiedError(error));
        }

        case 400: {
          const payloadError = new PayloadError(error);
          return Promise.reject(payloadError);
        }

        // Authentication
        case 401: {
          return Promise.reject(
            new UnauthorizedError('Authentication Error', error.code, status)
          );
        }

        // Authorization
        case 403: {
         const errorData = response?.data;

         if (errorData && errorData.type === 'permissions') {
          const event = new CustomEvent('showAccessDenied', {
              detail: {
                  message: errorData.message,
                  requiredRole: 'basic',
                  feature: 'courses'
              }
          });
          window.dispatchEvent(event);

          return Promise.reject(
            new UnauthorizedError('Acccess Denied', error.code, status)
          );
         } else {
          window.location.replace('/login');
          return Promise.reject(
             new UnauthorizedError('Authentication Required', error.code, status)
          )
         }

        }

        case 404: {
          return Promise.reject(new NotFoundError(error));
        }

        case 500: {
          const serverError = new ServerError(error);
          return Promise.reject(serverError);
        }

        default: {
          return Promise.reject(
            new Error((data as any).message || 'Unexpected error occurred')
          );
        }
      }
    } else if (request) {
      if (error.code === 'ETIMEDOUT' && error.message.includes('timeout')) {
        return Promise.reject(
          new Error('Request timeout: operation exceeded the set limit.')
        );
      } else {
        return Promise.reject(new Error('No response received from server.'));
      }
    } else {
      return Promise.reject(new Error(error.message));
    }
  }
);
export default httpClient;
