import DevShackClient from '../DevShackClient';
import { Artifact } from '../../types/Artifact';

const ArtifactsService = {
  /**
   * Gets all artifacts for the given user
   */
  getArtifacts: async () => DevShackClient.get<Artifact[]>(`/artifacts`),
  /**
   * Gets a single artifact by ID
   * @param id - The ID of the artifact to get
   * @returns The artifact
   */
  getArtifactById: async (id: string) =>
    DevShackClient.get<Artifact>(`/artifacts/${id}`),

  createArtifact: async (artifact: Artifact) =>
    DevShackClient.post<Artifact>(`/artifacts`, artifact),
  updateArtifact: async (id: string, artifact: Artifact) =>
    DevShackClient.put<Artifact>(`/artifacts/${id}`, artifact),
};

export default ArtifactsService;
