import DevShackClient from '../DevShackClient';
import routes from '../routes';

const CourseService = {
  getCourses: async () => DevShackClient.get(routes.COURSE),

  getCourseById: async (id: string, includeProgress = false) => {
    const url = `${routes.COURSE}/${id}${
      includeProgress ? '?includeProgress=true' : ''
    }`;
    return DevShackClient.get(url);
  },

  completeLesson: async (
    courseId: string,
    lessonId: string,
    position: number
  ) => {
    return DevShackClient.post(`${routes.COURSE}/progress/${courseId}`, {
      lessonId,
      position,
    });
  },
};

export default CourseService;
