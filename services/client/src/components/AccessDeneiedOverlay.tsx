import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

interface AccessDeniedOverlayProps{
    isOpen:boolean;
    onClose:() => void;
    requiredRole: string;
    feature:  string;
}

const AccessDeniedOverlay : React.FC<AccessDeniedOverlayProps> = ({
    isOpen,
    onClose,
    requiredRole,
    feature
}) => { 
    const { user } = useAuth();
    const navigate = useNavigate();

    const handleUpgrade =() => {
        console.log('upgrade functioning');
    };

    if(!isOpen) return null;

  return(
     
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-primary-900/80 backdrop-blur-md"></div>
      <div className="relative max-w-md w-full bg-secondary-50 rounded-2xl shadow-2xl overflow-hidden border border-spacedGrey-300">
      
        <div className="flex items-center justify-between p-6 border-b border-spacedGrey-300">
          <h2 className="text-xl font-semibold text-primary-900">Access Restricted</h2>
        </div>

        <div className="p-6 space-y-4">
          <div className="text-center">
            <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>         
            <p className="text-spacedGrey-700 mb-4">
              You are currently a <span className="font-semibold text-primary-800">{user?.role}</span> user. 
              To access <span className="font-semibold">{feature}</span>, you need 
              <span className="font-semibold text-neonGreen-600"> {requiredRole}</span> access or higher.
            </p>      
            <p className="text-sm text-spacedGrey-600">
              Upgrade your membership to unlock this feature and many more!
            </p>
          </div>
        </div>
        <div className="p-6 bg-spacedGrey-50 border-t border-spacedGrey-300 space-y-3">
          <button
            onClick={handleUpgrade}
            className="w-full bg-neonGreen-500 text-primary-900 py-3 px-6 rounded-lg font-semibold hover:bg-neonGreen-600 transition-colors duration-200"
          >
            Upgrade Account
          </button>
          <button
            onClick={()=>{
                 onClose();
                setTimeout(() => navigate('/'), 0);
            }}
            className="w-full bg-secondary-100 text-spacedGrey-700 py-3 px-6 rounded-lg font-medium hover:bg-secondary-200 transition-colors duration-200"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    </div>
    );
};
export default AccessDeniedOverlay;
