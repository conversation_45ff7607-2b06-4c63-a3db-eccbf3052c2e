import React from 'react';
import { Artifact } from '../types/Artifact';

interface ArtifactCardProps {
  artifact: Artifact;
}

const ArtifactCard: React.FC<ArtifactCardProps> = ({ artifact }) => {
  // Use the first available image from metadata. If none exists, use a placeholder.
  const imageUrl = artifact.metadata?.image?.url
    ? artifact.metadata.image.url
    : 'https://via.placeholder.com/400x200?text=No+Image';

  // Get the alt text for the image or fallback to the artifact title.
  const imageAlt = artifact.title;

  // Format the owner's name if available.
  const ownerName = artifact.owner
    ? `${artifact.owner.name.first} ${artifact.owner.name.last}`
    : 'Unknown Owner';

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow hover:shadow-lg transition-shadow">
      <img
        className="w-full h-40 object-cover rounded-t-lg"
        src={imageUrl}
        alt={imageAlt}
      />
      <div className="p-4">
        <h3 className="text-lg font-semibold text-gray-800">
          {artifact.title}
        </h3>
        {artifact.description && (
          <p className="text-gray-600 text-sm mt-2 line-clamp-3">
            {artifact.description}
          </p>
        )}
        <div className="mt-4 flex items-center justify-between">
          <span className="text-sm text-gray-500">{artifact.type}</span>
          <span className="text-sm text-gray-500">By {ownerName}</span>
        </div>
      </div>
    </div>
  );
};

export default ArtifactCard;
