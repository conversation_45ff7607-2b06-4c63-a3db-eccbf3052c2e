import React, { useState } from 'react';
import ReactPlayer from 'react-player';
import { Lesson } from '../types/Course';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

interface ContentPlayerProps {
  lesson: Lesson;
  onPreviousLesson?: () => void;
  onNextLesson?: () => void;
  onToggleComplete?: (lessonId: string) => void;
  isCompleted?: boolean;
  hasPrevious?: boolean;
  hasNext?: boolean;
}

const ContentPlayer: React.FC<ContentPlayerProps> = ({
  lesson,
  onPreviousLesson,
  onNextLesson,
  onToggleComplete,
  isCompleted = false,
  hasPrevious = false,
  hasNext = false,
}) => {
  const [ready, setReady] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="content-player">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold">{lesson.title}</h2>

        {/* Controls Panel */}
        <div className="flex gap-4">
          <button
            onClick={onPreviousLesson}
            disabled={!hasPrevious}
            className={`p-2 rounded-full bg-white shadow-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed`}
            aria-label="Previous lesson"
          >
            <ChevronLeftIcon className="h-6 w-6" />
          </button>

          <button
            onClick={onNextLesson}
            disabled={!hasNext}
            className={`p-2 rounded-full bg-white shadow-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed`}
            aria-label="Next lesson"
          >
            <ChevronRightIcon className="h-6 w-6" />
          </button>

          <button
            onClick={() => onToggleComplete?.(lesson.id)}
            className={`p-2 rounded-full bg-white shadow-lg hover:bg-gray-100 
              ${isCompleted ? 'text-green-500' : 'text-gray-400'}`}
            aria-label={isCompleted ? 'Mark as incomplete' : 'Mark as complete'}
          >
            <CheckCircleIcon className="h-6 w-6" />
          </button>
        </div>
      </div>
      <div className="relative">
        <div className="aspect-video bg-gray-100">
          <ReactPlayer
            url={lesson.videoLink}
            controls
            width="100%"
            height="100%"
            style={{
              opacity: ready ? 1 : 0,
              transition: 'opacity 0.3s ease-in-out',
            }}
            onReady={() => setReady(true)}
            onProgress={(state) => setCurrentTime(state.playedSeconds)}
            config={{
              youtube: {
                playerVars: { disablekb: 1 },
              },
            }}
          />
          {!ready && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
            </div>
          )}
        </div>
        <div className="mt-2 text-gray-600">
          Current Time: {formatTime(currentTime)}
        </div>
      </div>
    </div>
  );
};

export default ContentPlayer;
