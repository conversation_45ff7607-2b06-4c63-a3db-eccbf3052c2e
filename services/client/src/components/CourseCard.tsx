import React from 'react';
import { Course } from '../types/Course';
import Button from './lib/Button';
import Pill from './lib/Pill';
import Rating from './lib/Rating';
import pathfinderCover from '../assets/images/pathfinder-cover-test.png';

interface CourseCardProps {
  course: Course;
  onClick: () => void;
}

const CourseCard: React.FC<CourseCardProps> = ({ course, onClick }) => {
  return (
    <li key={course._id} className="relative">
      <div className="bg-white">
        <div
          className="group relative overflow-hidden rounded shadow-md hover:shadow-lg transition-shadow duration-300 focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 focus-within:ring-offset-gray-100 cursor-pointer"
          onClick={onClick}
        >
          <img
            alt={course.title}
            src={course.coverImage || pathfinderCover}
            className="pointer-events-none aspect-[16/9] object-cover group-hover:opacity-85"
          />
          <div className="absolute top-2 right-2">
            <Pill text="New" color="neonGreen" outline={false} />
          </div>
          <button type="button" className="absolute inset-0 focus:outline-none">
            <span className="sr-only">View details for {course.title}</span>
          </button>
        </div>
        <div className="mt-4 px-4 flex justify-between items-center">
          <p className="text-xs text-gray-700 underline font-semibold hover:text-blue-500">
            {course.enrolments} students
          </p>
          <Rating rating={course.rating} />
        </div>
        <div className="mt-2 p-4">
          <p className="truncate text-sm font-bold text-gray-800">
            {course.title}
          </p>
          <p className="mt-1 text-xs text-gray-500 truncate">
            {course.description}
          </p>
          <div className="mt-8 flex justify-end">
            <Button text="Continue" size="md" onClick={onClick} />
          </div>
        </div>
      </div>
    </li>
  );
};

export default CourseCard;
