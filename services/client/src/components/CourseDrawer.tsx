'use client';

import { Dialog, DialogPanel } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Course } from '../types/Course';

interface CourseDrawerProps {
  course: Course | null;
  onClose: () => void;
}

const CourseDrawer: React.FC<CourseDrawerProps> = ({ course, onClose }) => {
  return (
    <Dialog open={!!course} onClose={onClose} className="relative z-10">
      <div className="fixed inset-0 bg-black bg-opacity-30 transition-opacity" />
      

      <div className="fixed inset-0 overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10 sm:pl-16">
            
            <DialogPanel
  transition
  className="pointer-events-auto w-screen h-screen transform transition duration-500 ease-in-out bg-white shadow-xl"
>

              <div className="flex h-full flex-col overflow-y-scroll">
                <div className="px-4 py-6 sm:px-6">
                  <div className="flex items-start justify-between">
                    <h2 className="text-base font-semibold text-gray-900">
                      {course?.title}
                    </h2>
                    <button
                      type="button"
                      onClick={onClose}
                      className="relative rounded-md bg-white text-gray-400 hover:text-gray-500 focus:ring-2 focus:ring-indigo-500"
                    >
                      <span className="sr-only">Close panel</span>
                      <XMarkIcon className="size-6" aria-hidden="true" />
                    </button>
                  </div>
                </div>

                {/* Course Details */}
                {course && (
                  <div>
                    <div className="pb-1 sm:pb-6">
                      <div className="relative h-40 sm:h-56">
                        <img
                          alt={course.title}
                          src={course.coverImage}
                          className="absolute size-full object-cover"
                        />
                      </div>
                      <div className="mt-6 px-4 sm:mt-8 sm:flex sm:items-end sm:px-6">
                        <div className="sm:flex-1">
                          <p className="text-sm text-gray-500">
                            {course.description}
                          </p>
                          <p className="text-sm font-medium text-gray-700 mt-2">
                            Enrollments: {course.enrolments}
                          </p>
                          <p className="text-sm font-medium text-gray-700">
                            Rating: {course.rating} ⭐
                          </p>
                          <p className="text-sm font-medium text-gray-700">
                            Lessons: {course.lessonsCount}
                          </p>
                          <div className="mt-4">
                            <a
                              href={`/learning/${course._id}`}
                              className="inline-flex items-center justify-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                            >
                              Continue Learning
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Modules */}
                    <div className="px-4 pb-5 pt-5 sm:px-6">
                      <h3 className="text-lg font-semibold text-gray-900">
                        Modules
                      </h3>
                      <ul className="mt-2 space-y-2">
                        {course.modules.map((module) => (
                          <li
                            key={module.module}
                            className="text-sm text-gray-700"
                          >
                            <span className="font-medium">
                              Module {module.module}:
                            </span>{' '}
                            {module.title}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            </DialogPanel>
          </div>
        </div>
      </div>
    </Dialog>
  );
};

export default CourseDrawer;
