
import React, { useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useAccessControl } from '../context/AccessControlContext';
        
interface RequireRoleProps {
  children: JSX.Element;
  requiredRole: 'guest' | 'basic' | 'premium' | 'founder' | 'admin';
  feature: string;
}

const RequireRole: React.FC<RequireRoleProps> = ({ children, requiredRole, feature }) => {
  const { user } = useAuth();
  const { showAccessDenied } = useAccessControl();

  const roleHierarchy = ['guest', 'basic', 'premium', 'founder', 'admin'];
  const userRoleIndex = roleHierarchy.indexOf(user?.role || 'guest');
  const requiredRoleIndex = roleHierarchy.indexOf(requiredRole);

  useEffect(() => {
    if (userRoleIndex < requiredRoleIndex) {
      showAccessDenied(requiredRole, feature);
    }
  }, [userRoleIndex, requiredRoleIndex, requiredRole, feature, showAccessDenied]);

  return children;
};

export default RequireRole;
