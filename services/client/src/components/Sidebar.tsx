// src/components/Sidebar.tsx
import React from 'react';
import { useDnD } from '../context/DnDContext';

const Sidebar: React.FC = () => {
  const [_, setNodeType] = useDnD();

  const onDragStart = (
    event: React.DragEvent<HTMLDivElement>,
    type: string
  ) => {
    setNodeType(type);
    event.dataTransfer.effectAllowed = 'move';
  };

  return (
    <aside className="p-4 border-r" style={{ width: '250px' }}>
      <div className="mb-4 font-bold">Node Types</div>
      <div
        className="mb-2 p-2 border rounded cursor-pointer"
        draggable
        onDragStart={(e) => onDragStart(e, 'course')}
      >
        Course (Source Node)
      </div>
      <div
        className="mb-2 p-2 border rounded cursor-pointer"
        draggable
        onDragStart={(e) => onDragStart(e, 'group')}
      >
        Module (Group Node)
      </div>
      <div
        className="mb-2 p-2 border rounded cursor-pointer"
        draggable
        onDragStart={(e) => onDragStart(e, 'default')}
      >
        Artifact (Default Node)
      </div>
      <div
        className="mb-2 p-2 border rounded cursor-pointer"
        draggable
        onDragStart={(e) => onDragStart(e, 'output')}
      >
        Event (Output Node)
      </div>
    </aside>
  );
};

export default Sidebar;
