import React from 'react';
import List, { ListItem } from '../shared/ListComponent';

const items: ListItem[] = [
  { id: 1, description: '<PERSON>' },
  { id: 2, description: '<PERSON>' },
  { id: 3, description: '<PERSON>' },
];

const LeaderBoardComponent: React.FC = () => {
  return (
    <div className="p-4 border rounded">
      <h2 className="font-bold text-gray-500 mb-2">Leaderboard</h2>
      <List items={items} />
    </div>
  );
};

export default LeaderBoardComponent;
