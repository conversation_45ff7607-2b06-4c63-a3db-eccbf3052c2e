const items = [
  {
    id: 1,
    title: 'Node.js',
    description: 'Node.js is a runtime environment for JavaScript',
  },
  { id: 2, title: 'Docker', description: 'Docker is a containerization tool' },
  { id: 3, title: 'MongoDB', description: 'MongoDB is a NoSQL database' },
  {
    id: 4,
    title: 'React',
    description: 'React is a JavaScript library for building user interfaces',
  },
];

const PinnedCoursesComponent = () => {
  return (
    <div className="p-4 border rounded">
      <h2 className="font-bold text-gray-500 mb-4">Pinned Courses</h2>

      <ul role="list" className="divide-y divide-gray-200">
        {items.map((item) => (
          <li
            key={item.id}
            className="py-3 hover:bg-gray-50 flex justify-between items-center"
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                {/* <div className="w-6 h-6 rounded-lg bg-blue-100 flex items-center justify-center">
                  <svg
                    className="w-3 h-3 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                    />
                  </svg>
                </div> */}
              </div>
              <div className="max-w-[170px]">
                <h3 className="text-sm font-medium text-gray-900 truncate">
                  {item.description}
                </h3>
                {/* <p className="text-xs text-gray-500">
                  Brief description of the course content
                </p> */}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Active
              </span>
              <button className="text-gray-400 hover:text-gray-500">
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                  />
                </svg>
              </button>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default PinnedCoursesComponent;
