const teams = [
  { name: 'Discover', href: '#', initial: 'T1', current: true },
  { name: 'Build a course', href: '#', initial: 'T2', current: true },
  { name: 'Share Dev Shack', href: '#', initial: 'T3', current: true },
  { name: '<PERSON><PERSON><PERSON>', href: '#', initial: 'T4', current: true },
];

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

const QuickLinksComponent = () => {
  return (
    <div className="p-4 border rounded">
      <h2 className="font-bold text-gray-500 mb-2">Quick Links</h2>
      <ul role="list" className="divide-y divide-gray-200">
        <li>
          <ul role="list" className="-mx-2 mt-2">
            {teams.map((team) => (
              <li key={team.name}>
                <a
                  href={team.href}
                  className={classNames(
                    team.current
                      ? 'text-gray-600'
                      : 'text-gray-200 hover:bg-primary-700 hover:text-white',
                    'group flex gap-x-3 rounded-md p-2 text-sm/6'
                  )}
                >
                  <span className="flex size-6 shrink-0 items-center justify-center rounded-lg border border-primary-400 bg-primary-500 text-[0.625rem] font-medium text-white">
                    {team.initial}
                  </span>
                  <span className="truncate">{team.name}</span>
                </a>
              </li>
            ))}
          </ul>
        </li>
      </ul>
    </div>
  );
};

export default QuickLinksComponent;
