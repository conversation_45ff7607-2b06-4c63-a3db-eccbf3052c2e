import React from 'react';
import { Streaks } from '../../types/Person';

interface StreakComponentProps {
  streaks: Streaks;
}


const StreakComponent: React.FC<StreakComponentProps> = ({ streaks }) => {
const studySessionCount = typeof streaks?.activity?.studySession?.count === 'number' ? streaks?.activity?.studySession?.count : 0

  return (
    <div className="flex flex-col space-y-4 max-w-md mx-auto w-full">
      {/* Daily Streak Card */}
      <div className="bg-primary-800 p-6 rounded hover:shadow-lg transition-shadow duration-300">
        <h2 className="text-xl font-bold text-secondary-100 mb-2">
          Daily Streak
        </h2>
        <div className="flex items-baseline justify-start">
          <span className="text-5xl font-bold text-neonGreen-300">
            {streaks?.daily?.count || 0}
          </span>
          <span className="ml-2 text-neonGreen-300">days</span>
        </div>
        <p className="text-xs text-primary-400 mt-2">
          Last active:{' '}
          {new Date(streaks?.daily?.lastUpdated || '').toLocaleDateString(
            'en-US',
            {
              month: 'short',
              day: 'numeric',
              year: 'numeric',
            }
          )}
        </p>
      </div>

      {/* Study Sessions Streak Card */}
      <div className="bg-secondary-50 border rounded p-6 hover:shadow-lg transition-shadow duration-300">
        <h2 className="text-lg font-semibold text-gray-800 mb-2">
          Study Sessions
        </h2>
        <div className="flex items-baseline justify-start">
          <span className="text-5xl font-bold text-neonGreen-600">
            {studySessionCount}
          </span>
          <span className="ml-2 text-neonGreen-600">sessions</span>
        </div>
        <p className="text-xs text-gray-500 mt-2">
          Last session:{' '}
          {new Date(
            streaks?.activity?.studySession?.lastUpdated || ''
          ).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
          })}
        </p>
      </div>

      {/* Course Progress Streak Card */}
      {/* <div className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300">
        <h2 className="text-lg font-semibold text-gray-800 mb-2">
          Course Progress
        </h2>
        <div className="flex items-center justify-center">
          <span className="text-4xl font-bold text-amber-600">
            {streaks?.activity?.courseProgress?.count || 0}
          </span>
          <span className="ml-2 text-gray-600">updates</span>
        </div>
        <p className="text-sm text-gray-500 mt-2">
          Last update:{' '}
          {new Date(
            streaks?.activity?.courseProgress?.lastUpdated || ''
          ).toLocaleDateString()}
        </p>
      </div> */}
    </div>
  );
};

export default StreakComponent;
