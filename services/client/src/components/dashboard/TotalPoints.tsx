import React from 'react';
import { useAuth } from '../../context/AuthContext';

const TotalPoints: React.FC = () => {
  const auth = useAuth();
  const { points } = auth.user;

  return (
    <div className="bg-primary-800 p-4 border rounded text-center text-white">
      <h2 className="text-xl font-bold mb-2">Total Points</h2>
      <p className="text-5xl font-semibold">{points?.total || 0}</p>
    </div>
  );
};

export default TotalPoints;
