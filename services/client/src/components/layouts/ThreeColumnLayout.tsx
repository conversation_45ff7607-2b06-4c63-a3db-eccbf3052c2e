import { ReactNode } from 'react';

interface ThreeColumnLayoutProps {
  left: ReactNode;
  center: ReactNode;
  right: ReactNode;
  gridGap?: number; // Gap between columns in pixels
  leftColSpan?: number; // Number of columns for left panel (1-12)
  centerColSpan?: number; // Number of columns for center panel (1-12)
  rightColSpan?: number; // Number of columns for right panel (1-12)
  panelPadding?: number; // Padding inside each panel in pixels
}

export default function ThreeColumnLayout({
  left,
  center,
  right,
  gridGap = 4,
  leftColSpan = 3,
  centerColSpan = 6,
  rightColSpan = 3,
  panelPadding = 4,
}: ThreeColumnLayoutProps) {
  // Use CSS variable `--nav-height` for the top nav's height.
  // Falls back to 60px if the variable isn't defined.
  return (
    <div
      className="bg-gray-200 mx-auto"
      style={{ height: 'calc(100vh - var(--nav-height, 60px))' }}
    >
      <div className={`grid grid-cols-12 gap-${gridGap} h-full p-4`}>
        <div
          className={`col-span-${leftColSpan} bg-white rounded-md p-${panelPadding} h-full overflow-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']`}
        >
          {left}
        </div>
        <div
          className={`col-span-${centerColSpan} overflow-auto h-full [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']`}
        >
          {center}
        </div>
        <div
          className={`col-span-${rightColSpan} bg-white rounded-md p-${panelPadding} h-full overflow-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']`}
        >
          {right}
        </div>
      </div>
    </div>
  );
}
