import { PlusIcon } from '@heroicons/react/20/solid';
import { useNavigate } from 'react-router-dom';
const people = [
  {
    name: 'Node and Express',
    role: 'Front-end Developer',
    imageUrl: 'https://img-c.udemycdn.com/course/480x270/5820756_8a2d_5.jpg',
  },
  {
    name: 'CSS and Styling in Web',
    role: 'Designer',
    imageUrl: 'https://img-c.udemycdn.com/course/480x270/5820756_8a2d_5.jpg',
  },
  {
    name: 'MERN stack mastery',
    role: 'Full stack developer',
    imageUrl: 'https://img-c.udemycdn.com/course/480x270/5820756_8a2d_5.jpg',
  },
];

export default function EmptyCoursesComponent() {
  const navigate = useNavigate();
  return (
    <div className="mx-auto max-w-lg">
      <div>
        <div className="text-center">
          <svg
            fill="none"
            stroke="currentColor"
            viewBox="0 0 48 48"
            aria-hidden="true"
            className="mx-auto size-12 text-gray-400"
          >
            <path
              d="M34 40h10v-4a6 6 0 00-10.712-3.714M34 40H14m20 0v-4a9.971 9.971 0 00-.712-3.714M14 40H4v-4a6 6 0 0110.713-3.714M14 40v-4c0-1.313.253-2.566.713-3.714m0 0A10.003 10.003 0 0124 26c4.21 0 7.813 2.602 9.288 6.286M30 14a6 6 0 11-12 0 6 6 0 0112 0zm12 6a4 4 0 11-8 0 4 4 0 018 0zm-28 0a4 4 0 11-8 0 4 4 0 018 0z"
              strokeWidth={2}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <h2 className="mt-2 text-base font-semibold text-gray-900">
            Select a course to get started
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            To access the learning center, you need to select a course. You can
            choose any course that you have enrolled in.
          </p>
        </div>
        <form action="#" className="mt-6 flex">
          <input
            name="search"
            type="text"
            placeholder="Enter a course name"
            aria-label="Course name"
            className="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
          />
          <button
            type="submit"
            className="ml-4 shrink-0 rounded-md bg-primary-700 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            Search
          </button>
        </form>
      </div>
      <div className="mt-10">
        <h3 className="text-sm font-medium text-gray-500">
          Your latest courses you have enrolled in
        </h3>
        <ul
          role="list"
          className="mt-4 divide-y divide-gray-200 border-b border-t border-gray-200"
        >
          {people.map((person, personIdx) => (
            <li
              key={personIdx}
              className="flex items-center justify-between space-x-3 py-4"
            >
              <div className="flex min-w-0 flex-1 items-center space-x-3">
                <div className="shrink-0">
                  <img
                    alt=""
                    src={person.imageUrl}
                    className="size-10 rounded-full"
                  />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="truncate text-sm font-medium text-gray-900">
                    {person.name}
                  </p>
                  <p className="truncate text-sm font-medium text-gray-500">
                    {person.role}
                  </p>
                </div>
              </div>
              <div className="shrink-0">
                <button
                  type="button"
                  className="inline-flex items-center gap-x-1.5 text-sm/6 font-semibold text-gray-900"
                  onClick={() => {
                    navigate(`/learning/666ffdbc7189e097f4343631`);
                  }}
                >
                  <PlusIcon
                    aria-hidden="true"
                    className="size-5 text-gray-400"
                  />
                  Continue <span className="sr-only">{person.name}</span>
                </button>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}
