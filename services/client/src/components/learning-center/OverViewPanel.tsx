import React from 'react';
import VerticalProgress from '../vertical-progress.component';
import { Course, Lesson } from '../../types/Course';
import { PanelWithHeader } from '../shared/PanelWithHeader';
interface OverViewPanelProps {
  course: Course;
  setSelectedLesson: (lesson: Lesson) => void;
}

const OverViewPanel: React.FC<OverViewPanelProps> = ({
  course,
  setSelectedLesson,
}) => {
  return (
    <PanelWithHeader
      tabs={[
        { id: 'curriculum', label: 'Course Overview' },
        { id: 'progress', label: 'Progress' },
      ]}
      activeTab="curriculum"
    >
      <VerticalProgress
        modules={course.modules}
        currentModuleId={course.modules[0]._id} // TODO: Adjust as needed
        currentLessonId={course.modules[0].lessons[0]._id}
        onLessonSelect={(lesson) => setSelectedLesson(lesson)}
        completedLessons={course.userProgress?.completedLessons}
      />
    </PanelWithHeader>
  );
};

export default OverViewPanel;
