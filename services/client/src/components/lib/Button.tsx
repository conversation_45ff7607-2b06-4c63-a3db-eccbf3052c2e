import React from 'react';

interface ButtonProps {
  text: string;
  onClick?: () => void;
  size?: 'sm' | 'md' | 'lg';
}

// Define padding classes based on size prop
const sizeClasses: Record<'sm' | 'md' | 'lg', string> = {
  sm: 'px-2 py-1', // Small button padding
  md: 'px-3.5 py-1.5', // Default (medium) button padding
  lg: 'px-4 py-2', // Large button padding
};

const Button: React.FC<ButtonProps> = ({ text, onClick, size = 'md' }) => {
  const paddingClasses = sizeClasses[size]; // Determine which padding classes to use

  return (
    <button
      type="button"
      onClick={onClick}
      className={`rounded bg-gray-900 ${paddingClasses} text-sm font-semibold text-white shadow-sm hover:bg-primary-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600`}
    >
      {text}
    </button>
  );
};

export default Button;
