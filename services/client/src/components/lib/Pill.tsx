import React from 'react';

interface PillProps {
  text: string;
  /**
   * The base color (e.g., "blue", "red", "green")
   * This will determine the background, text, border, and hover colors.
   */
  color?: string;
  /**
   * Whether to show a border outline.
   * Defaults to true.
   */
  outline?: boolean;
  /**
   * Whether to show a drop shadow.
   * Defaults to true.
   */
  shadow?: boolean;
}

const Pill: React.FC<PillProps> = ({
  text,
  color,
  outline = true,
  shadow = true,
}) => {
  // Use the provided color to choose the appropriate Tailwind classes.
  // If no color is provided, fallback to the default white background and gray text/border.
  const bgColor = color ? `bg-${color}-500` : 'bg-white';
  const textColor = color ? `text-${color}-700` : 'text-gray-900';
  const ringColor = color ? `ring-${color}-500` : 'ring-gray-300';
  const hoverBgColor = color ? `hover:bg-${color}-100` : 'hover:bg-gray-50';

  const outlineClasses = outline ? `ring-1 ring-inset ${ringColor}` : '';
  const shadowClasses = shadow ? 'shadow-sm' : '';

  const pillClasses = [
    'rounded-full',
    'px-2.5',
    'py-1',
    'text-xs',
    'font-semibold',
    bgColor,
    textColor,
    outlineClasses,
    shadowClasses,
    hoverBgColor,
  ]
    .filter(Boolean) // Filter out any empty strings.
    .join(' ');

  return (
    <button type="button" className={pillClasses}>
      {text}
    </button>
  );
};

export default Pill;
