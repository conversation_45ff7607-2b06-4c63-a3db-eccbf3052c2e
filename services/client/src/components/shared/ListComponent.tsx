// List.tsx
import React from 'react';

export interface ListItem {
  id: string | number;
  description: string;
  // You can add other fields here if needed
}

interface ListProps {
  /** Array of items to list */
  items: ListItem[];
  /**
   * Optional custom renderer for each item.
   * If provided, this function will be called with each item,
   * and its return value will be rendered inside the list item.
   */
  renderItem?: (item: ListItem) => React.ReactNode;
}

const List: React.FC<ListProps> = ({ items, renderItem }) => {
  return (
    <ul role="list" className="divide-y divide-gray-200">
      {items.map((item) => (
        <li
          key={item.id}
          className="py-3 hover:bg-gray-50 flex justify-between items-center"
        >
          {renderItem ? (
            renderItem(item)
          ) : (
            <>
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  {/* Optional icon can go here */}
                  {/*
                    <div className="w-6 h-6 rounded-lg bg-blue-100 flex items-center justify-center">
                      <svg
                        className="w-3 h-3 text-blue-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                        />
                      </svg>
                    </div>
                    */}
                </div>
                <div className="max-w-[170px]">
                  <h3 className="text-sm font-medium text-gray-900 truncate">
                    {item.description}
                  </h3>
                  {/* Uncomment the block below if you need a subtitle */}
                  {/*
                    <p className="text-xs text-gray-500">
                      Brief description of the course content
                    </p>
                    */}
                </div>
              </div>
            </>
          )}
        </li>
      ))}
    </ul>
  );
};

export default List;
