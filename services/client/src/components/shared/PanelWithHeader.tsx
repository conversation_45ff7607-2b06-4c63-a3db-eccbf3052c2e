import React, { ReactNode } from 'react';

interface Tab {
  id: string;
  label: string;
}

interface HeaderButton {
  icon: ReactNode;
  onClick: () => void;
  ariaLabel: string;
}

interface PanelWithHeaderProps {
  tabs?: Tab[];
  activeTab?: string;
  onTabChange?: (tabId: string) => void;
  headerButtons?: HeaderButton[];
  children?: ReactNode;
  footer?: ReactNode;
  className?: string;
}

export const PanelWithHeader: React.FC<PanelWithHeaderProps> = ({
  tabs,
  activeTab,
  onTabChange,
  headerButtons,
  children,
  footer,
  className = '',
}) => {
  return (
    <div className={`overflow-hidden h-full flex flex-col ${className}`}>
      {/* Header */}
      {(tabs || headerButtons) && (
        <div className="border-b border-gray-200">
          <div className="flex justify-between items-center px-4 py-3">
            {/* Tabs */}
            {tabs && (
              <div className="flex space-x-4">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => onTabChange?.(tab.id)}
                    className={`px-3 py-2 text-sm font-medium ${
                      activeTab === tab.id
                        ? 'text-gray-900 border-b-2 border-primary-900'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </div>
            )}

            {/* Header Buttons */}
            {headerButtons && (
              <div className="flex space-x-2">
                {headerButtons.map((button, index) => (
                  <button
                    key={index}
                    onClick={button.onClick}
                    className="p-1 rounded hover:bg-gray-100"
                    aria-label={button.ariaLabel}
                  >
                    {button.icon}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Content Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">{children}</div>

      {/* Footer */}
      {footer && <div className="border-t border-gray-200 p-4">{footer}</div>}
    </div>
  );
};

export default PanelWithHeader;
