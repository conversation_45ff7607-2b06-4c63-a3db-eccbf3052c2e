import { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';

interface Lesson {
  lesson: number;
  title: string;
  content: string;
  videoLink: string;
  _id: string;
}

interface Module {
  module: number;
  title: string;
  lessons: Lesson[];
  _id: string;
}

interface VerticalProgressProps {
  modules: Module[];
  currentModuleId?: string;
  currentLessonId?: string;
  onLessonSelect?: (lesson: Lesson) => void;
  completedLessons?: string[];
}

export default function VerticalProgress({
  modules,
  currentModuleId,
  currentLessonId,
  onLessonSelect,
  completedLessons,
}: VerticalProgressProps) {
  const [expandedModules, setExpandedModules] = useState<string[]>([]);

  const toggleModule = (moduleId: string) => {
    setExpandedModules((prev) =>
      prev.includes(moduleId)
        ? prev.filter((id) => id !== moduleId)
        : [...prev, moduleId]
    );
  };

  const getModuleStatus = (moduleId: string) => {
    if (!currentModuleId) return 'upcoming';
    if (moduleId === currentModuleId) return 'current';
    // Check if all lessons in the module are completed
    const module = modules.find((m) => m._id === moduleId);
    if (
      module &&
      module.lessons.every((lesson) => completedLessons?.includes(lesson._id))
    ) {
      return 'complete';
    }
    const currentModuleIndex = modules.findIndex(
      (m) => m._id === currentModuleId
    );
    const thisModuleIndex = modules.findIndex((m) => m._id === moduleId);
    return thisModuleIndex < currentModuleIndex ? 'complete' : 'upcoming';
  };

  return (
    <div className="space-y-4">
      {modules.map((module) => {
        const status = getModuleStatus(module._id);
        const isExpanded = expandedModules.includes(module._id);

        return (
          <div
            key={module._id}
            className="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
          >
            {/* Module Card Header */}
            <button
              onClick={() => toggleModule(module._id)}
              className={classNames(
                'w-full px-4 py-3 flex items-center justify-between',
                'text-left transition-colors',
                status === 'current' ? 'bg-primary-800' : 'bg-white',
                status === 'complete' ? 'bg-gray-50' : ''
              )}
            >
              <div className="flex-1">
                <div className="flex items-start">
                  <div
                    className={classNames(
                      'w-3 h-3 rounded-full border-2 flex-shrink-0 mt-1',
                      status === 'complete'
                        ? 'border-neonGreen-500 bg-neonGreen-500'
                        : status === 'current'
                        ? 'border-primary-500'
                        : 'border-gray-300'
                    )}
                  />
                  <h3
                    className={classNames(
                      'font-medium ml-3',
                      status === 'current'
                        ? 'text-white'
                        : status === 'complete'
                        ? 'text-gray-900'
                        : 'text-gray-600'
                    )}
                  >
                    Module {module.module}: {module.title}
                  </h3>
                </div>

                <div className="flex items-center justify-between">
                  <p className="ml-6 text-sm text-gray-500">
                    {module.lessons.length} lessons
                  </p>
                </div>
              </div>
              {isExpanded ? (
                <ChevronUpIcon className="h-5 w-5 text-gray-400" />
              ) : (
                <ChevronDownIcon className="h-5 w-5 text-gray-400" />
              )}
            </button>

            {/* Lessons Accordion */}
            {isExpanded && (
              <div className="border-t">
                {module.lessons.map((lesson) => (
                  <button
                    key={lesson._id}
                    className={classNames(
                      'w-full px-4 py-2 text-left hover:bg-gray-50',
                      'border-b last:border-b-0',
                      currentLessonId === lesson._id ? 'bg-primary-800' : ''
                    )}
                    onClick={() => {
                      // Handle lesson selection
                      // You can add an onLessonSelect prop and call it here
                      onLessonSelect?.(lesson);
                    }}
                  >
                    <div className="flex items-center">
                      <div className="w-8 text-sm text-gray-500">
                        {lesson.lesson}
                      </div>
                      <div>{completedLessons?.includes(lesson._id)}</div>
                      <div className="flex-1 flex items-center justify-between">
                        <p
                          className={classNames(
                            'text-sm',
                            currentLessonId === lesson._id
                              ? 'text-secondary-600 font-medium'
                              : 'text-gray-700'
                          )}
                        >
                          {lesson.title}
                        </p>
                        {completedLessons?.includes(lesson._id) && (
                          <svg
                            className="h-5 w-5 text-neonGreen-500 ml-2"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        )}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}
