import React, { createContext, useContext, useState, ReactNode } from 'react';
import AccessDeniedOverlay from '../components/AccessDeneiedOverlay';

interface AccessControlContextType{
    showAccessDenied : ( requiredRole : string, feature: string ) => void;
    hideAccessDenied: () => void;
}

const AccessControlContext = createContext <AccessControlContextType | undefined>(undefined);

export const AccessControlProvider :React.FC<{ children : ReactNode }> = ({ children }) => {
    const [isOverlayOpen,setIsOverlayOpen] = useState(false);
    const [requiredRole, setRequiredRole] = useState('');
    const [feature, setFeature] = useState('')

    const showAccessDenied =( role : string, featureName: string ) =>{
        setRequiredRole(role);
        setFeature(featureName);
        setIsOverlayOpen(true);
    };

    const hideAccessDenied = () => {
        setIsOverlayOpen(false);
        setRequiredRole('');
        setFeature('');
    };

    return (
        <AccessControlContext.Provider value ={{showAccessDenied,hideAccessDenied}}>
            {children}
            <AccessDeniedOverlay
                isOpen = {isOverlayOpen}
                onClose = {hideAccessDenied}
                requiredRole = {requiredRole}
                feature = {feature}
                />
        </AccessControlContext.Provider>
    );
};

export const useAccessControl = () => {
    const context = useContext(AccessControlContext);
    if (!context) {
        throw new Error('useAccessCONtrol must be used within AccessControlProvider');
    }
    return context;
}
