// src/context/AuthContext.tsx
import {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from 'react';
import config from '../lib/config';
import AuthService from '../api/services/AuthService';
import UserService from '../api/services/UserService';
import { User } from '../types/User';

interface AuthContextType {
  user:User | null;
  loading: boolean;
  signin: (user: any, callback: VoidFunction) => void;
  signout: (callback: VoidFunction) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    // Exclude user check on the login page.
    if (window.location.pathname === '/login') {
      setLoading(false);
      return;
    }
    const fetchUser = async () => {
      try {
        const resp = await UserService.getUser();

        if (!resp) {
          if (resp.status === 401 || resp.status === 403) {
            // Optionally, you can handle this here or let your protected route component handle it.
            //window.location.href = '/login';
            return;
          }
          throw new Error('Failed to fetch user');
        }

        const userData = resp;
        setUser(userData);
      } catch (error) {
        console.error('Error fetching user:', error);
        //window.location.href = '/login';
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, []);

  // This signin function might not be used directly when using OAuth.
  const signin = (provider: string, callback: VoidFunction) => {
    //window.location.href = `${config.api.baseUrl}${config.api.prefix}/${provider}`;
    callback();
  };

  // For signout, you might also want to call your backend to clear cookies.
  const signout = (callback: VoidFunction) => {
    // Optionally, call an API endpoint to destroy the session.
    AuthService.logout();
    setUser(null);
    callback();
  };

  const value = { user, loading, signin, signout };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
