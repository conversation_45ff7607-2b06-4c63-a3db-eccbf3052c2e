// src/components/DnDContext.tsx
import React, { createContext, useContext, useState } from 'react';

type DnDContextType = [string | null, (type: string | null) => void];

const DnDContext = createContext<DnDContextType>([null, () => {}]);

export const DnDProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [nodeType, setNodeType] = useState<string | null>(null);
  return (
    <DnDContext.Provider value={[nodeType, setNodeType]}>
      {children}
    </DnDContext.Provider>
  );
};

export const useDnD = () => useContext(DnDContext);

export default DnDContext;
