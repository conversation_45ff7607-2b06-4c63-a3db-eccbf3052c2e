import VerticalProgress from '../components/vertical-progress.component';

export default function LearningCenter() {
  return (
    <div className="bg-gray-300 mx-auto sm:px-6 lg:px-8">
      <div className="grid grid-cols-12 gap-4 pt-12">
        <div className="col-span-3 h-[90vh]">
          <div className="overflow-hidden rounded-lg bg-white shadow">
            <div className="px-4 py-5 sm:px-6">
              <p>Progress</p>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:p-6">
              {' '}
              <VerticalProgress />
            </div>
          </div>
        </div>
        <div className="col-span-6">
          <div className="overflow-hidden rounded-lg bg-white shadow">
            <div className="px-4 py-5 sm:px-6">
              <p>Learning Center</p>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:p-6"></div>
          </div>
        </div>
        <div className="col-span-3">
          <div className="overflow-hidden rounded-lg bg-white shadow">
            <div className="px-4 py-5 sm:px-6">
              <p>Studio</p>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:p-6"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
