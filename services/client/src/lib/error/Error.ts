import { ApiError } from '../../types/ApiError';

export class BaseError extends Error implements ApiError {
  status: number;

  code: string | undefined;

  timestamp: number;

  type: string;

  data?: unknown;

  statusText?: string;

  url?: string;

  constructor(
    message: string,
    code: string | undefined,
    status: number,
    type: string,
    data?: unknown,
    statusText?: string,
    url?: string
  ) {
    super(message);
    this.status = status;
    this.timestamp = Date.now();
    this.type = type;
    this.code = code;
    this.data = data;
    this.statusText = statusText;
    this.url = url;
  }
}
