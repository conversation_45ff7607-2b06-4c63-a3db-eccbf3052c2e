import { AxiosError } from 'axios';

import { BaseError } from './Error';

export class NotFoundError extends BaseError {
  constructor(error: AxiosError) {
    const { response, code, message } = error;
    const status = response?.status || 404;
    const data = response?.data;
    const statusText = response?.statusText;
    const url = response?.config?.url;
    super(message, code, status, 'Not Found Error', data, statusText, url);
  }
}
