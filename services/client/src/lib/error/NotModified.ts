import { AxiosError } from 'axios';

import { BaseError } from './Error';

export class NotModifiedError extends BaseError {
  constructor(error: AxiosError) {
    const { response, code, message } = error;
    const status = response?.status || 304;
    const statusText = response?.statusText;
    const url = response?.config?.url;
    super(
      message,
      code,
      status,
      'Not Modified Error',
      undefined,
      statusText,
      url
    );
  }
}
