import { AxiosError } from 'axios';

import { BaseError } from './Error';

export class PayloadError extends BaseError {
  constructor(error: AxiosError) {
    const { response, code, message } = error;
    const status = response?.status || 400;
    const data = response?.data;
    const statusText = response?.statusText;
    const url = response?.config?.url;
    super(message, code, status, 'Payload Error', data, statusText, url);
  }
}
