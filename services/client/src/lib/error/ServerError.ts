import { AxiosError } from 'axios';

import { BaseError } from './Error';

export class ServerError extends BaseError {
  constructor(error: AxiosError) {
    const { response, code, message } = error;
    const status = response?.status || 500;
    const data = response?.data;
    const statusText = response?.statusText;
    const url = response?.config?.url;

    super(message, code, status, 'Server Error', data, statusText, url);
  }
}
