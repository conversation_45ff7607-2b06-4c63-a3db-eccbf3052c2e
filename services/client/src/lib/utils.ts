import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export const devshackTheme = {
  colors: {
    text: 'black', 
    textSecondary: 'black', 
    textMuted: 'gray-600', 
    accent: 'neonGreen', 
    background: 'white', 
    border: 'gray-200', 
    surface: 'white',
  },
  surface: {
    light: 'bg-white border border-gray-200',
    medium: 'bg-white border border-gray-200 shadow-sm',
    heavy: 'bg-white border border-gray-300 shadow-md',
    accent: 'bg-neonGreen-50 border border-neonGreen-200',
    muted: 'bg-white border border-gray-200',
  },
  shadows: {
    default: 'shadow',
    large: 'shadow-lg',
    neonGlow: 'shadow-neon-glow',
    neonGlowLarge: 'shadow-neon-glow-lg',
  },
  radius: {
    card: 'rounded-2xl',
    panel: 'rounded-3xl',
    button: 'rounded-xl',
    input: 'rounded-xl',
    pill: 'rounded-full',
  },
  animations: {
    float: 'animate-float',
    shimmer: 'animate-shimmer',
    glow: 'animate-pulse',
  }
} as const

export type DevShackTheme = typeof devshackTheme

// Surface variant generator
export function surfaceVariant(
  intensity: 'light' | 'medium' | 'heavy' | 'accent' | 'muted' = 'medium'
) {
  return devshackTheme.surface[intensity]
}

// Animation utilities
export function withAnimatedEffects(baseClasses: string, animated = true) {
  return cn(
    baseClasses,
    'transition-all duration-500',
    animated && 'hover:scale-[1.02] hover:shadow-lg'
  )
}


export function textVariant(type: 'primary' | 'secondary' | 'muted' | 'accent' = 'primary') {
  const variants = {
    primary: 'text-black font-medium', 
    secondary: 'text-black', 
    muted: 'text-gray-600 text-sm', 
    accent: 'text-neonGreen-500 font-semibold' 
  }
  return variants[type]
}

export function headerVariant(level: 'primary' | 'secondary' = 'primary') {
  const variants = {
    primary: 'text-2xl font-bold text-neonGreen-500', 
    secondary: 'text-xl font-semibold text-black' 
  }
  return variants[level]
}

export function accentVariant(intensity: 'light' | 'medium' | 'strong' = 'medium') {
  const variants = {
    light: 'bg-white text-black border-gray-200', 
    medium: 'bg-gray-50 text-black border-gray-200', 
    strong: 'bg-black text-white border-black' 
  }
  return variants[intensity]
}