// src/pages/CourseBuilder.tsx
import React, { useRef, useCallback, useState } from 'react';
import {
  ReactFlow,
  ReactFlowProvider,
  addEdge,
  useNodesState,
  useEdgesState,
  Background,
  MiniMap,
  Controls,
  useReactFlow,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import Sidebar from '../components/Sidebar';
import { DnDProvider, useDnD } from '../context/DnDContext';

let id = 0;
const getId = () => `dndnode_${id++}`;

//
// FlowCanvas: The center panel with the React Flow canvas
//
const FlowCanvas: React.FC<{
  onNodeClick: (event: React.MouseEvent, node: any) => void;
}> = ({ onNodeClick }) => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const { screenToFlowPosition } = useReactFlow();
  const [nodeType] = useDnD();

  const onConnect = useCallback(
    (connection: any) => setEdges((eds) => addEdge(connection, eds)),
    [setEdges]
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();
      if (!nodeType) return;

      // Convert screen position to flow position.
      const position = screenToFlowPosition({
        x: event.clientX,
        y: event.clientY,
      });

      let label = '';
      // Set a default label based on node type.
      switch (nodeType) {
        case 'course':
          label = 'Course Node';
          break;
        case 'group':
          label = 'Module Node';
          break;
        case 'default':
          label = 'Artifact Node';
          break;
        case 'output':
          label = 'Event Node';
          break;
        default:
          label = 'New Node';
      }

      const newNode = {
        id: getId(),
        type: nodeType, // Use the dragged node type
        data: { label },
        position,
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [screenToFlowPosition, nodeType, setNodes]
  );

  return (
    <div ref={reactFlowWrapper} className="flex-1" style={{ height: '100%' }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onDrop={onDrop}
        onDragOver={onDragOver}
        onNodeClick={onNodeClick}
        fitView
        style={{ width: '100%', height: '100%', backgroundColor: '#F7F9FB' }}
      >
        <Controls />
        <MiniMap />
        <Background color="#aaa" gap={16} />
      </ReactFlow>
    </div>
  );
};

//
// NodeDetails: The right panel for editing the selected node’s details
//
const NodeDetails: React.FC<{
  selectedNode: any;
  onNameChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}> = ({ selectedNode, onNameChange }) => {
  return (
    <div className="p-4 border-l" style={{ width: '250px' }}>
      {selectedNode ? (
        <div>
          <h2 className="text-xl font-bold mb-4">Node Details</h2>
          <label className="block mb-2">
            Name:
            <input
              type="text"
              value={selectedNode.data.label}
              onChange={onNameChange}
              className="mt-1 block w-full border border-gray-300 rounded px-2 py-1"
            />
          </label>
          {/* Add more fields for additional node properties as needed */}
        </div>
      ) : (
        <div>Select a node to edit its details</div>
      )}
    </div>
  );
};

//
// DnDFlow: Combines the Sidebar (left), FlowCanvas (center), and NodeDetails (right)
//
const DnDFlow: React.FC = () => {
  const [selectedNode, setSelectedNode] = useState<any>(null);

  const handleNodeClick = (event: React.MouseEvent, node: any) => {
    setSelectedNode(node);
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!selectedNode) return;
    const newLabel = e.target.value;
    // Update the selected node’s data locally.
    setSelectedNode((prev: any) => ({
      ...prev,
      data: { ...prev.data, label: newLabel },
    }));
    // (In a complete implementation you’d also update the node in the FlowCanvas nodes state.)
  };

  return (
    <div className="flex h-screen">
      {/* Left Sidebar: Node toolbox */}
      <Sidebar />
      {/* Center Panel: React Flow canvas */}
      <FlowCanvas onNodeClick={handleNodeClick} />
      {/* Right Sidebar: Node details editor */}
      <NodeDetails
        selectedNode={selectedNode}
        onNameChange={handleNameChange}
      />
    </div>
  );
};

//
// CourseBuilder: Wraps DnDFlow with providers.
//
const CourseBuilder: React.FC = () => {
  return (
    <ReactFlowProvider>
      <DnDProvider>
        <DnDFlow />
      </DnDProvider>
    </ReactFlowProvider>
  );
};

export default CourseBuilder;

// // src/pages/CourseBuilder.tsx
// import React, { useState } from 'react';
// import {
//   ReactFlow,
//   useNodesState,
//   useEdgesState,
//   Background,
//   MiniMap,
//   Controls,
// } from '@xyflow/react';
// import '@xyflow/react/dist/style.css';

// const CourseBuilder: React.FC = () => {
//   // Use the new hooks for managing nodes and edges.
//   const [nodes, setNodes, onNodesChange] = useNodesState([]);
//   const [edges, setEdges, onEdgesChange] = useEdgesState([]);
//   const [selectedNode, setSelectedNode] = useState<any>(null);

//   // Add a new module node.
//   const handleAddModule = () => {
//     const newNode = {
//       id: (nodes.length + 1).toString(),
//       type: 'default', // For a custom drag handle, you might switch this to a custom node type.
//       data: { label: 'New Module' },
//       position: { x: 100 + Math.random() * 200, y: 100 + Math.random() * 200 },
//     };
//     setNodes((nds) => [...nds, newNode]);
//   };

//   // Add a new artifact node.
//   const handleAddArtifact = () => {
//     const newNode = {
//       id: (nodes.length + 1).toString(),
//       type: 'default',
//       data: { label: 'New Artifact' },
//       position: { x: 100 + Math.random() * 200, y: 100 + Math.random() * 200 },
//     };
//     setNodes((nds) => [...nds, newNode]);
//   };

//   // When a node is clicked, set it as the selected node.
//   const onNodeClick = (event: React.MouseEvent, node: any) => {
//     setSelectedNode(node);
//   };

//   // When a connection is made, add a new edge.
//   const onConnect = (connection: any) => {
//     // Create a new edge object from the connection data.
//     const newEdge = {
//       id: `${connection.source}-${connection.target}`,
//       ...connection,
//     };
//     setEdges((eds) => [...eds, newEdge]);
//   };

//   // Update the selected node's label.
//   const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     if (!selectedNode) return;
//     const newLabel = e.target.value;

//     // Update the node in the nodes state.
//     setNodes((nds) =>
//       nds.map((node: any) =>
//         node.id === selectedNode.id
//           ? { ...node, data: { ...node.data, label: newLabel } }
//           : node
//       )
//     );

//     // Also update the local selected node state.
//     setSelectedNode((prev: any) =>
//       prev ? { ...prev, data: { ...prev.data, label: newLabel } } : null
//     );
//   };

//   return (
//     <div className="flex h-screen">
//       {/* Left Panel - Toolbox */}
//       <div className="w-1/4 border-r p-4">
//         <h2 className="text-xl font-bold mb-4">Toolbox</h2>
//         <button
//           onClick={handleAddModule}
//           className="w-full mb-2 px-4 py-2 bg-blue-500 text-white rounded"
//         >
//           Add Module
//         </button>
//         <button
//           onClick={handleAddArtifact}
//           className="w-full px-4 py-2 bg-green-500 text-white rounded"
//         >
//           Add Artifact
//         </button>
//       </div>

//       {/* Center Panel - React Flow Canvas */}
//       <div className="flex-1 p-4" style={{ height: '100%' }}>
//         <ReactFlow
//           nodes={nodes}
//           edges={edges}
//           onNodesChange={onNodesChange}
//           onEdgesChange={onEdgesChange}
//           onConnect={onConnect}
//           onNodeClick={onNodeClick}
//           fitView
//           style={{ width: '100%', height: '100%' }}
//         >
//           <Background color="#aaa" gap={16} />
//           <MiniMap />
//           <Controls />
//         </ReactFlow>
//       </div>

//       {/* Right Panel - Node Details */}
//       <div className="w-1/4 border-l p-4">
//         {selectedNode ? (
//           <div>
//             <h2 className="text-xl font-bold mb-4">Node Details</h2>
//             <label className="block mb-2">
//               Name:
//               <input
//                 type="text"
//                 value={selectedNode.data.label}
//                 onChange={handleNameChange}
//                 className="mt-1 block w-full border border-gray-300 rounded px-2 py-1"
//               />
//             </label>
//             {/* You can add more fields to edit additional node properties */}
//           </div>
//         ) : (
//           <div>Select a node to edit its details</div>
//         )}
//       </div>
//     </div>
//   );
// };

// export default CourseBuilder;
