import React, { useState, useEffect } from 'react';
import CourseService from '../api/services/CourseService';
import CourseCard from '../components/CourseCard';
import CourseDrawer from '../components/CourseDrawer';
import { Course } from '../types/Course';

const Courses: React.FC = () => {
  const [courses, setCourses] = useState<Course[]>([]);
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);

  useEffect(() => {
    const fetchCourses = async () => {
      const courses = await CourseService.getCourses();
      setCourses(courses);
    };

    fetchCourses();
  }, []);

  return (
    <div className="px-20 py-8">
      <h1 className="text-2xl font-bold">Courses</h1>

      <div className="mt-8">
        <ul
          role="list"
          className="grid grid-cols-2 gap-x-4 gap-y-8 sm:grid-cols-3 sm:gap-x-6 lg:grid-cols-4 xl:gap-x-8"
        >
          {courses?.map((course) => (
            <CourseCard
              key={course._id}
              course={course}
              onClick={() => setSelectedCourse(course)}
            />
          ))}
        </ul>
      </div>

      {/* Drawer opens when a course is selected */}
      <CourseDrawer
        course={selectedCourse}
        onClose={() => setSelectedCourse(null)}
      />
    </div>
  );
};

export default Courses;
