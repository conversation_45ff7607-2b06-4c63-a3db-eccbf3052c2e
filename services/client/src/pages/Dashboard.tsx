import React from 'react';
import ThreeColumnLayout from '../components/layouts/ThreeColumnLayout';
import StreakComponent from '../components/dashboard/StreakComponent';
import LeaderBoardComponent from '../components/dashboard/LeaderBoardComponent';
import imageOne from '../assets/images/node-docker-mongo-key-note.001.jpeg';
import imageThree from '../assets/images/node-docker-mongo-key-note.003.jpeg';
import imageFive from '../assets/images/node-docker-mongo-key-note.005.jpeg';
import TotalPoints from '../components/dashboard/TotalPoints';
import { useAuth } from '../context/AuthContext';
import SkillBadgeComponent from '../components/dashboard/SkillBadgeComponent';
import PinnedCoursesComponent from '../components/dashboard/PinnedCoursesComponent';
import QuickLinksComponent from '../components/dashboard/QuickLinksComponent';
import { ChevronDownIcon } from '@heroicons/react/16/solid';

const Dashboard: React.FC = () => {
  const auth = useAuth();
  const { streaks } = auth.user;

  // Left Panel: Pinned Courses, Skill Badges, and Quick Links
  const leftPanel = (
    <div className="space-y-4">
      {/* Pinned Courses Section */}
      <PinnedCoursesComponent />
      <SkillBadgeComponent />
      <QuickLinksComponent />
    </div>
  );

  // Center Panel: Feed Posts
  const feedPosts = [
    {
      id: 1,
      title: 'Introduction to Node.js, Docker, and MongoDB Course',
      text: 'I created this course to help you learn how to use Node.js, Docker, and MongoDB together. It pulls together great resources across blogs, websites and youtube videos in a structured order to help you get all those knowledge gaps filled... Oh and I also add all the extra notes.',
      image: imageOne,
    },
    {
      id: 2,
      title: 'Getting Started with Docker Containerization',
      text: "Today we'll dive into containerization with Docker. We'll learn how to create Dockerfiles, manage containers, and set up a development environment that's consistent across all platforms. No more \"it works on my machine\" problems!",
      image: null,
    },
    {
      id: 3,
      title: 'MongoDB Deep Dive: Document Design and Performance',
      text: "Deep dive into MongoDB! We'll cover document design, indexing strategies, and how to structure your data for optimal performance. Plus, we'll look at some real-world examples of when to use embedded documents vs references.",
      image: imageThree,
    },
    {
      id: 4,
      title: 'Best Practices for Node.js and MongoDB Connections',
      text: "Quick tip: When working with Node.js and MongoDB, always use connection pooling and make sure to handle your database connections properly. Here's a guide on best practices for production-ready applications.",
      image: null,
    },
    {
      id: 5,
      title: 'New Docker Compose Section Added',
      text: 'Exciting update! Just added a new section on Docker Compose for managing multi-container applications. Learn how to orchestrate your Node.js backend, MongoDB database, and other services all together!',
      image: imageFive,
    },
  ];

  const tabs = [
    { name: 'All', href: '#', current: true },
    { name: 'Unread', href: '#', current: false },
    { name: 'Highest Rated', href: '#', current: false },
    { name: 'Newest', href: '#', current: false },
  ];

  function classNames(...classes: string[]) {
    return classes.filter(Boolean).join(' ');
  }

  const centerPanel = (
    <div className="space-y-4">
      <div className="sticky top-0 bg-white px-3 py-2 rounded">
        <div>
          <div className="grid grid-cols-1 sm:hidden">
            {/* Use an "onChange" listener to redirect the user to the selected tab URL. */}
            <select
              defaultValue={tabs?.find((tab) => tab.current)?.name}
              aria-label="Select a tab"
              className="col-start-1 row-start-1 w-full appearance-none rounded-md bg-white py-2 pl-3 pr-8 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600"
            >
              {tabs.map((tab) => (
                <option key={tab.name}>{tab.name}</option>
              ))}
            </select>
            <ChevronDownIcon
              aria-hidden="true"
              className="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end fill-gray-500"
            />
          </div>
          <div className="hidden sm:block">
            <nav aria-label="Tabs" className="flex space-x-4">
              {tabs.map((tab) => (
                <a
                  key={tab.name}
                  href={tab.href}
                  aria-current={tab.current ? 'page' : undefined}
                  className={classNames(
                    tab.current
                      ? 'bg-gray-100 text-gray-700'
                      : 'text-gray-500 hover:text-gray-700',
                    'rounded-md px-3 py-2 text-sm font-medium'
                  )}
                >
                  {tab.name}
                </a>
              ))}
            </nav>
          </div>
        </div>
      </div>
      {feedPosts.map((post) => (
        <div key={post.id} className="rounded bg-white">
          <div className="px-4 pt-4 flex justify-between items-baseline mb-4">
            <p className="text-primary-800 font-semibold text-lg">
              {post.title}
            </p>
            <p className="text-primary-800 font-normal text-xs">12 Nov 2025</p>
          </div>
          {post.image && (
            <img
              src={post.image}
              alt={`Post ${post.id} image`}
              className="mb-6 w-full aspect-16/9 object-cover"
            />
          )}
          <div className="px-4 pb-4">
            <p className="mb-6 text-primary-800 font-normal">{post.text}</p>
            <div className="flex space-x-6">
              <button className="py-1 text-xs text-bold text-primary-600 hover:underline">
                Like
              </button>
              <button className="py-1 text-xs text-bold text-primary-600 hover:underline">
                Share
              </button>
              <button className="py-1 text-xs text-bold text-primary-600 hover:underline">
                Comment
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  // Right Panel: Total Points, Leaderboard, and Daily Streak
  const rightPanel = (
    <div className="space-y-4">
      <TotalPoints />
      <LeaderBoardComponent />
      <StreakComponent streaks={streaks} />
    </div>
  );

  return (
    <ThreeColumnLayout
      left={leftPanel}
      center={centerPanel}
      right={rightPanel}
    />
  );
};

export default Dashboard;
