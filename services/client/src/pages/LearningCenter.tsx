import { useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import CourseService from '../api/services/CourseService';
import { Course, Lesson } from '../types/Course';
import ContentPlayer from '../components/ContentPlayer';
import ThreeColumnLayout from '../components/layouts/ThreeColumnLayout';
import EmptyCoursesComponent from '../components/learning-center/EmptyCoursesComponent';
import OverViewPanel from '../components/learning-center/OverViewPanel';
import { UserCourseProgress } from '../types/Course';

export default function LearningCenter() {
  const { courseId } = useParams();
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedLesson, setSelectedLesson] = useState<Lesson | null>(null);
  const [userProgress, setUserProgress] = useState<UserCourseProgress | null>(
    null
  );

  useEffect(() => {
    const fetchCourse = async () => {
      if (!courseId) return;

      try {
        setLoading(true);
        const courseData = await CourseService.getCourseById(courseId, true);
        setCourse(courseData);

        if (courseData.userProgress) {
          setUserProgress(courseData.userProgress);
          // Find the current lesson in all modules
          const currentLesson = courseData.modules
            .flatMap((module) => module.lessons)
            .find(
              (lesson: Lesson) =>
                lesson._id === courseData.userProgress.currentLesson
            );

          setSelectedLesson(currentLesson || courseData.modules[0].lessons[0]);
        } else {
          // If no progress, start with first lesson of first module
          setSelectedLesson(courseData.modules[0].lessons[0]);
        }
      } catch (error) {
        console.error('Error fetching course:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCourse();
  }, [courseId]);

  const handlePreviousLesson = () => {
    if (!course || !selectedLesson) return;
    const allLessons = course.modules.flatMap((module) => module.lessons);
    const currentIndex = allLessons.findIndex(
      (lesson) => lesson._id === selectedLesson._id
    );
    if (currentIndex > 0) {
      setSelectedLesson(allLessons[currentIndex - 1]);
    }
  };

  const handleNextLesson = () => {
    if (!course || !selectedLesson) return;
    const allLessons = course.modules.flatMap((module) => module.lessons);
    const currentIndex = allLessons.findIndex(
      (lesson) => lesson._id === selectedLesson._id
    );
    if (currentIndex < allLessons.length - 1) {
      setSelectedLesson(allLessons[currentIndex + 1]);
    }
  };

  const handleToggleComplete = async (lessonId: string) => {
    if (!userProgress || !course) return;

    try {
      const isCompleted = userProgress.completedLessons.includes(lessonId);
      let newCompletedLessons: string[];

      if (isCompleted) {
        newCompletedLessons = userProgress.completedLessons.filter(
          (id) => id !== lessonId
        );
      } else {
        newCompletedLessons = [...userProgress.completedLessons, lessonId];
      }

      // Update local state immediately for better UX
      setUserProgress((prev) =>
        prev
          ? {
              ...prev,
              completedLessons: newCompletedLessons,
            }
          : null
      );

      // Here you would typically make an API call to update the progress
      // await CourseService.updateLessonProgress(courseId, lessonId, !isCompleted);
    } catch (error) {
      console.error('Error updating lesson progress:', error);
      // Revert the local state if the API call fails
      setUserProgress((prev) =>
        prev
          ? {
              ...prev,
              completedLessons: userProgress.completedLessons,
            }
          : null
      );
    }
  };

  if (loading) {
    return <div>Loading...</div>; // You might want to replace this with a proper loading component
  }

  if (!course) {
    return (
      <div
        className="flex items-center justify-center"
        style={{ height: 'calc(100vh - var(--nav-height, 60px))' }}
      >
        <div className="max-w-7xl">
          <EmptyCoursesComponent />
        </div>
      </div>
    );
  }

  return (
    <ThreeColumnLayout
      gridGap={4}
      panelPadding={0}
      left={
        <OverViewPanel course={course} setSelectedLesson={setSelectedLesson} />
      }
      center={
        <div className="overflow-hidden rounded-lg bg-white shadow">
          <div className="bg-gray-50 px-4 py-5 sm:p-6 min-h-[calc(100vh-96px)]">
            {selectedLesson ? (
              <ContentPlayer
                lesson={selectedLesson}
                onPreviousLesson={handlePreviousLesson}
                onNextLesson={handleNextLesson}
                onToggleComplete={handleToggleComplete}
                isCompleted={
                  userProgress?.completedLessons.includes(selectedLesson._id) ||
                  false
                }
                hasPrevious={
                  course?.modules
                    .flatMap((m) => m.lessons)
                    .findIndex((l) => l._id === selectedLesson._id) > 0
                }
                hasNext={
                  course?.modules
                    .flatMap((m) => m.lessons)
                    .findIndex((l) => l._id === selectedLesson._id) <
                  course?.modules.flatMap((m) => m.lessons).length - 1
                }
              />
            ) : (
              <div>Select a lesson to start learning.</div>
            )}
          </div>
        </div>
      }
      right={
        <div className="overflow-hidden h-full flex flex-col">
          {/* Top Menu Bar */}
          <div className="border-b border-gray-200">
            <div className="flex justify-between items-center px-4 py-3">
              {/* Right side tabs */}
              <div className="flex space-x-4">
                <button className="px-3 py-2 text-sm font-medium text-gray-900 border-b-2 border-primary-900">
                  Ask
                </button>
                <button className="px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">
                  Notes
                </button>
                <button className="px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">
                  Resources
                </button>
              </div>
              {/* Left side icons */}
              <div className="flex space-x-2">
                <button className="p-1 rounded hover:bg-gray-100">
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 12h16m-7 6h7"
                    />
                  </svg>
                </button>
                <button className="p-1 rounded hover:bg-gray-100">
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {/* Chat Area */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {/* Chat messages will go here */}
          </div>

          {/* Input Area */}
          <div className="border-t border-gray-200 p-4">
            <div className="flex space-x-4">
              <input
                type="text"
                placeholder="Type your message..."
                className="flex-1 rounded-lg border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
              <button className="bg-primary-800 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                Send
              </button>
            </div>
          </div>
        </div>
      }
    />
  );
}
