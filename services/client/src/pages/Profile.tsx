
import { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { cn, devshackTheme, surfaceVariant, textVariant, headerVariant, accentVariant } from '@/lib/utils';
import {
  User,
  Shield,
  Bell,
  CreditCard,
  Users,
  Edit3,
  UserCircle
} from 'lucide-react';

const navigationItems = [
  { id: 'general', name: 'General', icon: UserCircle },
  { id: 'security', name: 'Security', icon: Shield },
  { id: 'notifications', name: 'Notifications', icon: Bell },
  { id: 'billing', name: 'Billing', icon: CreditCard },
  { id: 'team', name: 'Team members', icon: Users },
];

export default function Profile() {
  const { user, loading } = useAuth();
  const [activeTab, setActiveTab] = useState('general');
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editField, setEditField] = useState('');

  const fullName = `${user?.name.first} ${user?.name.last}`;
  const email = user?.email || '';
  const role = user?.role || '';
  const profilePicture = user?.picture || 'https://via.placeholder.com/150';

  if (loading) {
    return (
      <div className="container mx-auto max-w-4xl p-6 space-y-6">
        <Skeleton className="h-8 w-48" />
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <Skeleton className="h-96 lg:col-span-1" />
          <Skeleton className="h-96 lg:col-span-3" />
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto max-w-md p-6">
        <Card className={cn(surfaceVariant('medium'),cn(accentVariant('medium')), "border-destructive/20")}>
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-destructive/10 rounded-full flex items-center justify-center mb-4">
              <User className="w-6 h-6 text-destructive" />
            </div>
            <CardTitle className="text-destructive">Error loading user</CardTitle>
            <CardDescription>Unable to fetch profile details</CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-6xl p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className={cn(headerVariant('primary'))}>Profile Settings</h1>
          <p className={cn(textVariant('secondary'))}>Manage your account settings and preferences</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Navigation Sidebar */}
        <Card className={cn(surfaceVariant('medium'), "lg:col-span-1 h-[300px] lg:sticky lg:top-6")}>
          <CardHeader>
            <div className="flex items-center space-x-4">
              <Avatar className="w-16 h-16">
                <AvatarImage src={profilePicture} alt={fullName} />
                <AvatarFallback className="bg-gray-100 text-black font-semibold">
                  {user?.name.first?.[0]}{user?.name.last?.[0]}
                </AvatarFallback>
              </Avatar>
              <div>
                <Badge variant="secondary" className={cn(surfaceVariant('light'), "text-black px-4 py-2 text-lg")}>
                  {role}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className='space-y-6'>
            <Tabs value={activeTab} onValueChange={setActiveTab} orientation="vertical">
              <TabsList className="grid w-full bg-transparent border-none p-0">
                {navigationItems.map((item) => (
                  <TabsTrigger
                    key={item.id}
                    value={item.id}
                    className="justify-start gap-2 data-[state=active]:bg-gray-100 data-[state=active]:text-black data-[state=active]:font-bold"
                  >
                    <item.icon className="w-4 h-4" />
                    {item.name}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          </CardContent>
        </Card>

        {/* Main Content */}
        <div className="lg:col-span-3 space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsContent value="general" className="space-y-6">
              <Card className={cn(surfaceVariant('medium'))}>
                <CardHeader>
                  <CardTitle className={cn(headerVariant('secondary'))}>Personal Information</CardTitle>
                  <CardDescription className={cn(textVariant('secondary'))}>
                    Update your personal details and profile information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="fullName">Full Name</Label>
                      <div className="flex items-center justify-between p-3 rounded-lg border bg-muted/50">
                        <span>{fullName}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setEditField('name');
                            setEditDialogOpen(true);
                          }}
                        >
                          <Edit3 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <div className="flex items-center justify-between p-3 rounded-lg border bg-muted/50">
                        <span>{email}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setEditField('email');
                            setEditDialogOpen(true);
                          }}
                        >
                          <Edit3 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-6">
              <Card className={cn(surfaceVariant('medium'))}>
                <CardHeader>
                  <CardTitle>Security Settings</CardTitle>
                  <CardDescription>
                    Manage your password and security preferences
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">Security settings will be implemented here.</p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notifications" className="space-y-6">
              <Card className={cn(surfaceVariant('medium'))}>
                <CardHeader>
                  <CardTitle>Notification Preferences</CardTitle>
                  <CardDescription>
                    Configure how you receive notifications
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">Notification settings will be implemented here.</p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="billing" className="space-y-6">
              <Card className={cn(surfaceVariant('medium'))}>
                <CardHeader>
                  <CardTitle>Billing Information</CardTitle>
                  <CardDescription>
                    Manage your subscription and payment methods
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">Billing settings will be implemented here.</p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="team" className="space-y-6">
              <Card className={cn(surfaceVariant('medium'))}>
                <CardHeader>
                  <CardTitle>Team Members</CardTitle>
                  <CardDescription>
                    Manage team members and permissions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">Team management will be implemented here.</p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Edit Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className={cn(surfaceVariant('heavy'))}>
          <DialogHeader>
            <DialogTitle>Edit {editField}</DialogTitle>
            <DialogDescription>
              Update your {editField} information
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="editValue">New {editField}</Label>
              <Input
                id="editValue"
                placeholder={`Enter new ${editField}`}
                className={cn(surfaceVariant('light'))}
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={() => setEditDialogOpen(false)}>
                Save Changes
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

