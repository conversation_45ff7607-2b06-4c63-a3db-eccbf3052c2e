import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import logo from '../assets/dsca-icon-black.png';
import DevShackClient from '../api/DevShackClient';
import { ServicePrefix } from '../types/ServicePrefix';

const Signup: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    debugger;
    if (user?.activated) {
      navigate('/');
    }
  }, [user, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await DevShackClient.put(
        `/user/${user._id}`,
        { activated: true },
        ServicePrefix.API
      );
      navigate('/');
    } catch (error: any) {
      console.error('Failed to update user activation:', error);

      if (axios.isAxiosError(error)) {
        const backendMessage =
          error.response?.data?.message ||
          error.response?.data?.error ||
          'Something went wrong. Please try again.';
        setError(backendMessage);
      } else {
        setError('Unexpected error. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  if (!user) return <div>Loading...</div>;

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white shadow-md rounded-lg p-8 space-y-6">
        <div className="flex justify-center mb-4">
          <img src={logo} alt="Logo" className="w-20" />
        </div>

        <h2 className="text-center text-3xl font-extrabold text-gray-900">
          Complete Your Signup
        </h2>
        <p className="text-center text-gray-600 text-sm">
          Welcome {`${user.name.first} ${user.name.last}`}! Please accept the terms to finish setting up your account.
        </p>

        <form className="space-y-6" onSubmit={handleSubmit}>
          <div className="flex items-center">
            <input
              id="terms"
              name="terms"
              type="checkbox"
              checked={acceptedTerms}
              onChange={(e) => setAcceptedTerms(e.target.checked)}
              className="h-4 w-4 text-indigo-600 border-gray-300 rounded"
            />
            <label htmlFor="terms" className="ml-2 block text-sm text-gray-900">
              I accept the{' '}
              <a href="#" className="text-indigo-600 underline">Terms & Conditions</a> and{' '}
              <a href="#" className="text-indigo-600 underline">Code of Conduct</a>
            </label>
          </div>

          {error && (
            <p className="text-red-600 text-sm text-center">{error}</p>
          )}

          <button
            type="submit"
            disabled={!acceptedTerms || loading}
            className={`w-full flex justify-center items-center gap-2 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
              acceptedTerms && !loading ? 'bg-slate-900 hover:bg-indigo-700' : 'bg-gray-400 cursor-not-allowed'
            } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500`}
          >
            {loading && (
              <svg
                className="animate-spin h-5 w-5 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
                ></path>
              </svg>
            )}
            {loading ? 'Activating...' : 'Complete Signup'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default Signup;
