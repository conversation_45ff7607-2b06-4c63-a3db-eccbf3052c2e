export interface Artifact {
  _id?: string;
  settings?: {
    favorite?: boolean;
  };
  title: string;
  description?: string;
  url: string;
  type: string;
  owner: {
    name: {
      first: string;
      last: string;
    };
    _id: string;
    email: string;
    picture: string;
  };
  tags?: string[];
  notes?: string[];
  status?: string;
  created: number;
  updated: number;
  linkedTopics?: string[]; // Array of Topic IDs
  createdBy?: string; // Should be a user ID
  updatedBy?: string; // Should be a user ID
  permissions?: {
    accessLevel?: string;
    sharedWith?: string[]; // Array of User IDs
    roles?: {
      viewer?: string[]; // Array of User IDs
      editor?: string[]; // Array of User IDs
      admin?: string[]; // Array of User IDs
    };
  };
  metadata?: {
    image?: {
      url: string;
      height: number;
      width: number;
    };
    video?: {
      url: string;
      height: number;
      width: number;
      tag: string;
    };
    title: string;
    description: string;
    site: string;
  };
  likes?: number;
  shares?: number;
}

export interface ArtifactRequest {
  title: string;
  url: string;
  type: string;
  settings?: object;
  description?: string;
  tags?: string[];
  notes?: string[];
  status?: string;
  permissions?: object;
}
