export interface Lesson {
  _id: string;
  lesson: number;
  title: string;
  content: string;
  videoLink: string;
}

export interface Module {
  module: number;
  title: string;
  description: string;
  lessons: Lesson[];
}

export interface Course {
  _id: string;
  published: boolean;
  coverImage: string;
  creator: string;
  title: string;
  description: string;
  modules: Module[];
  enrolments: number;
  rating: number;
  settings: {
    tier: string;
  };
  userProgress?: UserCourseProgress;
  modulesCount: number;
  lessonsCount: number;
}

export interface UserCourseProgress {
  _id: string;
  userId: string;
  courseId: string;
  completedLessons: string[];
  lastPosition: number;
  totalLessons: number;
  currentLesson: string;
  progress: number;
}
