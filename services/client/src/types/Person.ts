export interface Points {
  total: number;
  history: unknown[];
}

export interface ActivityStreaks {
  studySession: {
    count: number;
    lastUpdated: string;
  };
  courseProgress: {
    count: number;
    lastUpdated: string;
  };
}

export interface Streaks {
  daily: {
    count: number;
    lastUpdated: string;
  };
  activity: ActivityStreaks;
}

export interface PersonResponse {
  _id: string;
  name: {
    first: string;
    last: string;
  };
  email: string;
  language: string;
  locale: string;
  picture: string;
  role: string;
  points: Points;
  level: number;
  streaks: Streaks;
}

export default PersonResponse;
