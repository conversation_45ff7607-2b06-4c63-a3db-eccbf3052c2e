export interface User {
  _id: string;
  name: {
    first: string;
    last: string;
  };
  email: string;
  role: 'guest' | 'basic' | 'premium' | 'founder' | 'admin';
  points: {
    total: number;
    history: unknown[];
  };
  level: number;
  streaks: {
    daily: {
      count: number;
      lastUpdated: string;
    };
    activity: {
      studySession: {
        count: number;
        lastUpdated: string;
      };
      courseProgress: {
        count: number;
        lastUpdated: string;
      };
    };
  };
  picture?: string;
  language?: string;
  locale?: string;
  gender?: 'male' | 'female' | 'other' | 'undefined';
  membership?: {
    plan: {
      name: string;
      id: string;
    };
    status: string;
    startDate: string;
    endDate: string;
  };
  marketing?: {
    email: boolean;
    sms: boolean;
    push: boolean;
  };
  created?: string;
  updated?: string;
  activated?: boolean;
  banned?: boolean;
}
