/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f5f5f5',
          100: '#edf3c1',
          200: '#d9d9d9',
          300: '#c4c4c4',
          400: '#9e9e9e',
          500: '#7c7c7c',
          600: '#565656',
          700: '#444444',
          800: '#272727',
          900: '#010101', 
        }, // Black Light
        secondary: {
          50: '#ffffff', 
          100: '#fafafa',
          200: '#f5f5f5',
          300: '#dedede',
          400: '#c2c2c2',
          500: '#979797',
          600: '#818181',
          700: '#606060',
          800: '#3c3c3c',
          900: '#010101',
        }, // White
        spacedGrey: {
          50: '#f7f7f7',
          100: '#efefef',
          200: '#e3e3e3',
          300: '#d1d1d1',
          400: '#adadad',
          500: '#8c8c8c', 
          600: '#646464',
          700: '#515151',
          800: '#333333',
          900: '#141414',
        }, // Spaced Grey
        neonGreen: {
          50: '#f8fbe6', 
          100: '#edf3c1', 
          200: '#e2ec98',
          300: '#d6e56f',
          400: '#cdde4f',
          500: '#c5d92c', 
          600: '#b8c726',
          700: '#a8b11d',
          800: '#989a15',
          900: '#7d7404',
        }, // Neon Green
        softwareBlue: {
          50: '#c4cbe4', 
          100: '#c4cbe4', 
          200: '#9ea9d1',
          300: '#7888bf',
          400: '#5a6eb1',
          500: '#3d55a5', 
          600: '#374d9c',
          700: '#2e4490',
          800: '#263a83',
          900: '#1a296d',
        }, // Software Blue
      },
     
      backdropBlur: {
        'xs': '2px',
        'sm': '4px',
        'md': '8px',
        'lg': '12px',
        'xl': '16px',
        '2xl': '24px',
        '3xl': '40px',
      },
      backgroundImage: {
        'gradient': 'linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05))',
        'border': 'linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1))',
      },
      boxShadow: {
        'sm': '0 4px 16px 0 rgba(31, 38, 135, 0.25)',
        'lg': '0 16px 64px 0 rgba(31, 38, 135, 0.45)',
        'inset': 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1)',
        'neon-glow': '0 0 20px rgba(197, 217, 44, 0.3)',
        'neon-glow-lg': '0 0 40px rgba(197, 217, 44, 0.4)',
      },
    },
  },
  plugins: [],
};